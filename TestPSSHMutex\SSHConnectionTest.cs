using System;
using System.Threading;
using System.Threading.Tasks;
using PSSHConnMutex;

namespace TestPSSHMutex
{
    /// <summary>
    /// Test class to demonstrate the SSH connection improvements for socket error 10055
    /// </summary>
    public class SSHConnectionTest
    {
        public static void RunConnectionTest()
        {
            Console.WriteLine("=== SSH Connection Test for Socket Error 10055 Fix ===");
            Console.WriteLine();

            // Test connection manager
            TestConnectionManager();

            // Test multiple concurrent connections
            TestConcurrentConnections();

            Console.WriteLine("Test completed. Press any key to exit...");
            Console.ReadKey();
        }

        private static void TestConnectionManager()
        {
            Console.WriteLine("1. Testing Connection Manager:");
            
            string testHost = "test-server.example.com";
            
            // Test acquiring connections
            for (int i = 1; i <= 12; i++)
            {
                bool acquired = SSHConnectionManager.TryAcquireConnection(testHost);
                Console.WriteLine($"   Connection {i}: {(acquired ? "Acquired" : "Rejected")} - " +
                                $"Host connections: {SSHConnectionManager.GetConnectionCount(testHost)}, " +
                                $"Total: {SSHConnectionManager.GetTotalConnections()}");
                
                if (!acquired)
                {
                    Console.WriteLine("   Connection limit reached as expected.");
                    break;
                }
            }

            // Release some connections
            Console.WriteLine("   Releasing 5 connections...");
            for (int i = 0; i < 5; i++)
            {
                SSHConnectionManager.ReleaseConnection(testHost);
            }
            
            Console.WriteLine($"   After release - Host connections: {SSHConnectionManager.GetConnectionCount(testHost)}, " +
                            $"Total: {SSHConnectionManager.GetTotalConnections()}");
            
            // Clean up
            SSHConnectionManager.ForceCleanup();
            Console.WriteLine("   Connection manager test completed.");
            Console.WriteLine();
        }

        private static void TestConcurrentConnections()
        {
            Console.WriteLine("2. Testing Concurrent Connection Handling:");
            
            // Simulate multiple threads trying to connect
            Task[] tasks = new Task[20];
            
            for (int i = 0; i < tasks.Length; i++)
            {
                int taskId = i;
                tasks[i] = Task.Run(() => SimulateConnection(taskId));
            }

            Task.WaitAll(tasks);
            Console.WriteLine("   Concurrent connection test completed.");
            Console.WriteLine();
        }

        private static void SimulateConnection(int taskId)
        {
            string host = $"server-{taskId % 3}.example.com"; // Use 3 different hosts
            
            try
            {
                Console.WriteLine($"   Task {taskId}: Attempting connection to {host}");
                
                if (SSHConnectionManager.TryAcquireConnection(host))
                {
                    Console.WriteLine($"   Task {taskId}: Connection acquired for {host}");
                    
                    // Simulate work
                    Thread.Sleep(1000 + (taskId * 100));
                    
                    SSHConnectionManager.ReleaseConnection(host);
                    Console.WriteLine($"   Task {taskId}: Connection released for {host}");
                }
                else
                {
                    Console.WriteLine($"   Task {taskId}: Connection rejected for {host} (limit reached)");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   Task {taskId}: Error - {ex.Message}");
            }
        }

        /// <summary>
        /// Demonstrates the improved error handling for socket error 10055
        /// </summary>
        public static void DemonstrateErrorHandling()
        {
            Console.WriteLine("=== Error Handling Demonstration ===");
            Console.WriteLine();
            
            Console.WriteLine("Key improvements made to handle socket error 10055:");
            Console.WriteLine("1. Connection retry with exponential backoff");
            Console.WriteLine("2. Reduced timeouts (from 1 hour to 1 minute)");
            Console.WriteLine("3. Connection pooling and limits");
            Console.WriteLine("4. Proper resource cleanup");
            Console.WriteLine("5. Garbage collection on socket errors");
            Console.WriteLine("6. Graceful session termination");
            Console.WriteLine();
            
            Console.WriteLine("Socket Error 10055 (WSAENOBUFS) occurs when:");
            Console.WriteLine("- System runs out of socket buffer space");
            Console.WriteLine("- Too many connections created without proper cleanup");
            Console.WriteLine("- Network resources are exhausted");
            Console.WriteLine();
            
            Console.WriteLine("Our fixes address these issues by:");
            Console.WriteLine("- Limiting concurrent connections per host (max 10)");
            Console.WriteLine("- Global connection limit (max 50)");
            Console.WriteLine("- Automatic retry with delays");
            Console.WriteLine("- Forced garbage collection on socket errors");
            Console.WriteLine("- Improved connection cleanup");
            Console.WriteLine();
        }
    }
}
