﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{370EF740-65BE-4989-97C2-A484C1BBEE84}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>PSSHMutexConn</RootNamespace>
    <AssemblyName>PSSHMutexConn</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BouncyCastle.Crypto">
      <HintPath>..\ThirdParty\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="Jscape.Ssh">
      <HintPath>..\ThirdParty\Jscape.Ssh.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=1.2.9.0, Culture=neutral, PublicKeyToken=b32731d11ce58905">
      <HintPath>..\ThirdParty\log4net.dll</HintPath>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="Rebex.Castle">
      <HintPath>C:\Users\<USER>\Desktop\No Common Algorithm\Rebex.Castle.dll</HintPath>
    </Reference>
    <Reference Include="Rebex.Common, Version=2.0.5171.0, Culture=neutral, PublicKeyToken=1c4638788972655d, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Users\<USER>\Desktop\No Common Algorithm\RebexLatest\Rebex.Common.dll</HintPath>
    </Reference>
    <Reference Include="Rebex.Curve25519">
      <HintPath>C:\Users\<USER>\Desktop\No Common Algorithm\Rebex.Curve25519.dll</HintPath>
    </Reference>
    <Reference Include="Rebex.Ed25519">
      <HintPath>C:\Users\<USER>\Desktop\No Common Algorithm\Rebex.Ed25519.dll</HintPath>
    </Reference>
    <Reference Include="Rebex.Networking, Version=3.0.5171.0, Culture=neutral, PublicKeyToken=1c4638788972655d, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Users\<USER>\Desktop\No Common Algorithm\RebexLatest\Rebex.Networking.dll</HintPath>
    </Reference>
    <Reference Include="Rebex.Sftp, Version=3.0.4700.0, Culture=neutral, PublicKeyToken=1c4638788972655d, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Users\<USER>\Desktop\No Common Algorithm\RebexLatest\Rebex.Sftp.dll</HintPath>
    </Reference>
    <Reference Include="Rebex.SshShell, Version=1.0.5171.0, Culture=neutral, PublicKeyToken=1c4638788972655d, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Users\<USER>\Desktop\No Common Algorithm\RebexLatest\Rebex.SshShell.dll</HintPath>
    </Reference>
    <Reference Include="Rebex.Terminal, Version=1.0.5171.0, Culture=neutral, PublicKeyToken=1c4638788972655d, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Users\<USER>\Desktop\No Common Algorithm\RebexLatest\Rebex.Terminal.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CustomExceptions.cs" />
    <Compile Include="GlobalSuppressions.cs" />
    <Compile Include="PSSHMutex.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>