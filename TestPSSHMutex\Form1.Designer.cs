﻿namespace TestPSSHMutex
{
    partial class Form1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.label3 = new System.Windows.Forms.Label();
            this.txtoutput = new System.Windows.Forms.RichTextBox();
            this.btnRun = new System.Windows.Forms.Button();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label6 = new System.Windows.Forms.Label();
            this.txtWaitTime = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.lblprpassword = new System.Windows.Forms.Label();
            this.lblpruser = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.txtCommand = new System.Windows.Forms.TextBox();
            this.txtPrompt = new System.Windows.Forms.TextBox();
            this.txtport = new System.Windows.Forms.TextBox();
            this.txtPRPASS = new System.Windows.Forms.TextBox();
            this.txtPRUSER = new System.Windows.Forms.TextBox();
            this.txtPRIP = new System.Windows.Forms.TextBox();
            this.btnconnect = new System.Windows.Forms.Button();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(479, 69);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(70, 13);
            this.label3.TabIndex = 19;
            this.label3.Text = "Result output";
            // 
            // txtoutput
            // 
            this.txtoutput.Location = new System.Drawing.Point(470, 85);
            this.txtoutput.Name = "txtoutput";
            this.txtoutput.Size = new System.Drawing.Size(377, 270);
            this.txtoutput.TabIndex = 21;
            this.txtoutput.Text = "";
            // 
            // btnRun
            // 
            this.btnRun.Location = new System.Drawing.Point(291, 409);
            this.btnRun.Name = "btnRun";
            this.btnRun.Size = new System.Drawing.Size(90, 23);
            this.btnRun.TabIndex = 20;
            this.btnRun.Text = "Run";
            this.btnRun.UseVisualStyleBackColor = true;
            this.btnRun.Click += new System.EventHandler(this.btnRun_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Controls.Add(this.txtWaitTime);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.lblprpassword);
            this.groupBox1.Controls.Add(this.lblpruser);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.txtCommand);
            this.groupBox1.Controls.Add(this.txtPrompt);
            this.groupBox1.Controls.Add(this.txtport);
            this.groupBox1.Controls.Add(this.txtPRPASS);
            this.groupBox1.Controls.Add(this.txtPRUSER);
            this.groupBox1.Controls.Add(this.txtPRIP);
            this.groupBox1.Location = new System.Drawing.Point(133, 60);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(321, 313);
            this.groupBox1.TabIndex = 18;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "Production";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(22, 244);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(55, 13);
            this.label6.TabIndex = 14;
            this.label6.Text = "Wait Time";
            // 
            // txtWaitTime
            // 
            this.txtWaitTime.Location = new System.Drawing.Point(122, 244);
            this.txtWaitTime.Name = "txtWaitTime";
            this.txtWaitTime.Size = new System.Drawing.Size(170, 20);
            this.txtWaitTime.TabIndex = 13;
            this.txtWaitTime.Text = "350000";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(22, 207);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(54, 13);
            this.label2.TabIndex = 12;
            this.label2.Text = "Command";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(22, 169);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(40, 13);
            this.label5.TabIndex = 8;
            this.label5.Text = "Prompt";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(22, 132);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(26, 13);
            this.label4.TabIndex = 7;
            this.label4.Text = "Port";
            // 
            // lblprpassword
            // 
            this.lblprpassword.AutoSize = true;
            this.lblprpassword.Location = new System.Drawing.Point(22, 92);
            this.lblprpassword.Name = "lblprpassword";
            this.lblprpassword.Size = new System.Drawing.Size(53, 13);
            this.lblprpassword.TabIndex = 7;
            this.lblprpassword.Text = "Password";
            // 
            // lblpruser
            // 
            this.lblpruser.AutoSize = true;
            this.lblpruser.Location = new System.Drawing.Point(22, 54);
            this.lblpruser.Name = "lblpruser";
            this.lblpruser.Size = new System.Drawing.Size(29, 13);
            this.lblpruser.TabIndex = 6;
            this.lblpruser.Text = "User";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(22, 19);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(58, 13);
            this.label1.TabIndex = 1;
            this.label1.Text = "IP Address";
            // 
            // txtCommand
            // 
            this.txtCommand.Location = new System.Drawing.Point(122, 207);
            this.txtCommand.Name = "txtCommand";
            this.txtCommand.Size = new System.Drawing.Size(170, 20);
            this.txtCommand.TabIndex = 2;
            this.txtCommand.Text = "hostname";
            // 
            // txtPrompt
            // 
            this.txtPrompt.Location = new System.Drawing.Point(122, 169);
            this.txtPrompt.Name = "txtPrompt";
            this.txtPrompt.Size = new System.Drawing.Size(170, 20);
            this.txtPrompt.TabIndex = 5;
            this.txtPrompt.Text = "#";
            // 
            // txtport
            // 
            this.txtport.Location = new System.Drawing.Point(122, 132);
            this.txtport.Name = "txtport";
            this.txtport.Size = new System.Drawing.Size(170, 20);
            this.txtport.TabIndex = 4;
            this.txtport.Text = "22";
            // 
            // txtPRPASS
            // 
            this.txtPRPASS.Location = new System.Drawing.Point(122, 92);
            this.txtPRPASS.Name = "txtPRPASS";
            this.txtPRPASS.PasswordChar = '*';
            this.txtPRPASS.Size = new System.Drawing.Size(170, 20);
            this.txtPRPASS.TabIndex = 3;
            this.txtPRPASS.Text = "root123";
            // 
            // txtPRUSER
            // 
            this.txtPRUSER.Location = new System.Drawing.Point(122, 54);
            this.txtPRUSER.Name = "txtPRUSER";
            this.txtPRUSER.Size = new System.Drawing.Size(170, 20);
            this.txtPRUSER.TabIndex = 2;
            this.txtPRUSER.Text = "root";
            this.txtPRUSER.TextChanged += new System.EventHandler(this.txtPRUSER_TextChanged);
            // 
            // txtPRIP
            // 
            this.txtPRIP.Location = new System.Drawing.Point(122, 19);
            this.txtPRIP.Name = "txtPRIP";
            this.txtPRIP.Size = new System.Drawing.Size(170, 20);
            this.txtPRIP.TabIndex = 1;
            this.txtPRIP.Text = "**************";
            this.txtPRIP.TextChanged += new System.EventHandler(this.txtPRIP_TextChanged);
            // 
            // btnconnect
            // 
            this.btnconnect.Location = new System.Drawing.Point(291, 458);
            this.btnconnect.Name = "btnconnect";
            this.btnconnect.Size = new System.Drawing.Size(75, 23);
            this.btnconnect.TabIndex = 22;
            this.btnconnect.Text = "connect";
            this.btnconnect.UseVisualStyleBackColor = true;
            this.btnconnect.Click += new System.EventHandler(this.btnconnect_Click);
            // 
            // Form1
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(892, 493);
            this.Controls.Add(this.btnconnect);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.txtoutput);
            this.Controls.Add(this.btnRun);
            this.Controls.Add(this.groupBox1);
            this.Name = "Form1";
            this.Text = "Form1";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.RichTextBox txtoutput;
        private System.Windows.Forms.Button btnRun;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.TextBox txtWaitTime;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label lblprpassword;
        private System.Windows.Forms.Label lblpruser;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox txtCommand;
        private System.Windows.Forms.TextBox txtPrompt;
        private System.Windows.Forms.TextBox txtport;
        private System.Windows.Forms.TextBox txtPRPASS;
        private System.Windows.Forms.TextBox txtPRUSER;
        private System.Windows.Forms.TextBox txtPRIP;
        private System.Windows.Forms.Button btnconnect;
    }
}

