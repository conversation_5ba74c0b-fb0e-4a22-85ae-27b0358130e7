﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using log4net;
using Rebex.Net;
using Rebex.IO;
using Rebex.TerminalEmulation;
using System.Threading;
using System.Text.RegularExpressions;
using System.IO;
using Jscape.Ssh;
using Jscape.Ssh.Transport;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Security;
using Org.BouncyCastle.Crypto.Parameters;
using System.Configuration;
using Rebex.Security.Cryptography;
using System.Collections.Concurrent;

namespace PSSHConnMutex
{
    #region Connection Pool Manager
    /// <summary>
    /// Manages SSH connection limits to prevent socket exhaustion (error 10055)
    /// </summary>
    public static class SSHConnectionManager
    {
        private static readonly ILog Enginelog = LogManager.GetLogger("BcmsEngineLog");
        private static readonly ConcurrentDictionary<string, int> _activeConnections = new ConcurrentDictionary<string, int>();
        private static readonly object _lockObject = new object();
        private static readonly int _maxConnectionsPerHost = 10; // Limit connections per host
        private static readonly int _globalMaxConnections = 50; // Global connection limit
        private static int _totalActiveConnections = 0;

        /// <summary>
        /// Attempts to acquire a connection slot for the specified host
        /// </summary>
        /// <param name="host">The SSH host</param>
        /// <returns>True if connection slot acquired, false if limit reached</returns>
        public static bool TryAcquireConnection(string host)
        {
            lock (_lockObject)
            {
                // Check global limit
                if (_totalActiveConnections >= _globalMaxConnections)
                {
                    Enginelog.Warn($"Global connection limit reached ({_globalMaxConnections}). Cannot create new connection to {host}");
                    return false;
                }

                // Check per-host limit
                int currentConnections = _activeConnections.GetOrAdd(host, 0);
                if (currentConnections >= _maxConnectionsPerHost)
                {
                    Enginelog.Warn($"Connection limit for host {host} reached ({_maxConnectionsPerHost}). Cannot create new connection");
                    return false;
                }

                // Acquire connection
                _activeConnections[host] = currentConnections + 1;
                _totalActiveConnections++;

                Enginelog.Info($"Connection acquired for {host}. Host connections: {currentConnections + 1}, Total: {_totalActiveConnections}");
                return true;
            }
        }

        /// <summary>
        /// Releases a connection slot for the specified host
        /// </summary>
        /// <param name="host">The SSH host</param>
        public static void ReleaseConnection(string host)
        {
            lock (_lockObject)
            {
                if (_activeConnections.TryGetValue(host, out int currentConnections) && currentConnections > 0)
                {
                    _activeConnections[host] = currentConnections - 1;
                    _totalActiveConnections--;

                    Enginelog.Info($"Connection released for {host}. Host connections: {currentConnections - 1}, Total: {_totalActiveConnections}");

                    // Clean up if no connections for this host
                    if (_activeConnections[host] == 0)
                    {
                        _activeConnections.TryRemove(host, out _);
                    }
                }
            }
        }

        /// <summary>
        /// Gets the current connection count for a host
        /// </summary>
        /// <param name="host">The SSH host</param>
        /// <returns>Number of active connections</returns>
        public static int GetConnectionCount(string host)
        {
            return _activeConnections.GetOrAdd(host, 0);
        }

        /// <summary>
        /// Gets the total number of active connections
        /// </summary>
        /// <returns>Total active connections</returns>
        public static int GetTotalConnections()
        {
            return _totalActiveConnections;
        }

        /// <summary>
        /// Forces cleanup of all connection tracking (use with caution)
        /// </summary>
        public static void ForceCleanup()
        {
            lock (_lockObject)
            {
                _activeConnections.Clear();
                _totalActiveConnections = 0;
                Enginelog.Warn("Forced cleanup of all connection tracking");
            }
        }
    }
    #endregion Connection Pool Manager

    #region SSHConnFlag
    public enum SSHConnFlag
    {
        //none = 0,
        JSession = 1,
        RSession = 2,
    }
    #endregion SSHConnFlag

    #region SubAuthentication

    public enum SubstituteAuthentication
    {
        //none = 0,
        SudoSu = 1,
        Su = 2,
        Asu = 3,
        Sudo = 4,
        Privrun = 5,
        Other = 6
    }

    public class SubAuthentication : IDisposable
    {
        private static readonly ILog Enginelog = LogManager.GetLogger("BcmsEngineLog");
        public static SSHConnFlag sshConnOpt = (SSHConnFlag)Enum.Parse(typeof(SSHConnFlag), ConfigurationManager.AppSettings["SSHConnFlag"].ToString(), true);

        public int SubAuthenticationType { get; set; }
        public string SubUser { get; set; }
        public string SubPassword { get; set; }
        public string SubPath { get; set; }
        public static string LoginCommand { get; set; }
        public bool _isDisposed;

        public SubAuthentication()
        {
            log4net.Config.XmlConfigurator.Configure();
        }

        public SubAuthentication(int subAuthenticationType, string subUser, string subPassword, string subPath)
        {
            SubAuthenticationType = subAuthenticationType;
            SubUser = subUser;
            SubPassword = subPassword;
            SubPath = subPath;
        }

        public dynamic ProcessSubAuthentication(SSHServerInfo _server, dynamic _session, String shellPrompt)
        {

            int SubAuthenticationType = 0;
            string command = string.Empty;
            LoginCommand = string.Empty;
            string output = string.Empty;
            Regex _reg = new Regex(shellPrompt);

            if (sshConnOpt == SSHConnFlag.JSession)
            {
                Enginelog.Info(" ProcessSubAuthentication with JSession for ServerIP: " + _server.SSHHost);
                //client = SubAuthentication.ProcessSubAuthentication(_server, _session, shellPrompt);
                if (_server.SubAuthenticationList != null && _server.SubAuthenticationList.Count > 0)
                {
                    foreach (var SubAuthentication in _server.SubAuthenticationList)
                    {
                        SubAuthenticationType = SubAuthentication.SubAuthenticationType;

                        switch (SubAuthentication.SubAuthenticationType)
                        {
                            case (int)SubstituteAuthentication.SudoSu:

                                Enginelog.Info("SUDO SU SubAuthentication Type serverIP: " + _server.SSHHost);

                                command = SubAuthentication.SubPath + "sudo su - " + SubAuthentication.SubUser;
                                Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command);
                                if (!string.IsNullOrEmpty(SubAuthentication.SubPassword))
                                {
                                    output = _session.SendWait(command, ":", false, 120000);
                                    Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);
                                    _session.SendWait(SubAuthentication.SubPassword, shellPrompt, true, 120000);
                                }
                                else
                                {
                                    output = _session.SendWait(command, shellPrompt, true, 120000);
                                    Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);
                                }
                                Enginelog.Info("SUDO SU SubAuthentication Type serverIP: " + _server.SSHHost + " Completed");
                                break;
                            case (int)SubstituteAuthentication.Su:

                                Enginelog.Info("SU SubAuthentication Type serverIP: " + _server.SSHHost);

                                command = SubAuthentication.SubPath + "su - " + SubAuthentication.SubUser;
                                Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command);

                                if (!string.IsNullOrEmpty(SubAuthentication.SubPassword))
                                {
                                    output = _session.SendWait(command, ":", false, 120000);
                                    Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);

                                    _session.SendWait(SubAuthentication.SubPassword, shellPrompt, true, 120000);
                                }
                                else
                                {
                                    output = _session.SendWait(command, shellPrompt, true, 120000);
                                    Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);
                                }
                                Enginelog.Info("SU SubAuthentication Type serverIP: " + _server.SSHHost + " Completed");
                                break;
                            case (int)SubstituteAuthentication.Sudo:
                                LoginCommand = SubAuthentication.SubPath + "sudo ";
                                break;

                            case (int)SubstituteAuthentication.Privrun:
                                LoginCommand = SubAuthentication.SubPath + "privrun ";
                                break;

                            case (int)SubstituteAuthentication.Asu:
                                Enginelog.Info("ASU SubAuthentication Type serverIP: " + _server.SSHHost);

                                command = SubAuthentication.SubPath + "asu " + SubAuthentication.SubUser;
                                Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command);

                                if (!string.IsNullOrEmpty(SubAuthentication.SubPassword))
                                {
                                    output = _session.SendWait(command, ":", false, 120000);
                                    Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);
                                    _session.SendWait(SubAuthentication.SubPassword, shellPrompt, true, 120000);
                                }
                                else
                                {
                                    output = _session.SendWait(command, shellPrompt, true, 120000);
                                    Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);

                                }
                                Enginelog.Info("ASU SubAuthentication Type serverIP: " + _server.SSHHost + " Completed");
                                break;

                            case (int)SubstituteAuthentication.Other:
                                Enginelog.Info("ASU SubAuthentication Type serverIP: " + _server.SSHHost);

                                command = SubAuthentication.SubPath + " " + SubAuthentication.SubUser;
                                Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command);

                                if (!string.IsNullOrEmpty(SubAuthentication.SubPassword))
                                {
                                    output = _session.SendWait(command, ":", false, 120000);
                                    Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);
                                    _session.SendWait(SubAuthentication.SubPassword, shellPrompt, true, 120000);
                                }
                                else
                                {
                                    output = _session.SendWait(command, shellPrompt, true, 120000);
                                    Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);

                                }
                                Enginelog.Info("ASU SubAuthentication Type serverIP: " + _server.SSHHost + " Completed");
                                break;
                        }
                    }
                }
                return _session;
            }
            else
            {
                Enginelog.Info(" ProcessSubAuthentication with RSession for ServerIP: " + _server.SSHHost);
                VirtualTerminal terminal = null;
                //terminal.Bind(_session);
                terminal = _session;
                Thread.Sleep(1000);
                //client = SubAuthentication.ProcessSubAuthentication(__server, terminal, shellPrompt);
                if (_server.SubAuthenticationList != null && _server.SubAuthenticationList.Count > 0)
                {
                    foreach (var SubAuthentication in _server.SubAuthenticationList)
                    {
                        SubAuthenticationType = SubAuthentication.SubAuthenticationType;


                        switch (SubAuthentication.SubAuthenticationType)
                        {
                            case (int)SubstituteAuthentication.SudoSu:

                                command = SubAuthentication.SubPath + "sudo su - " + SubAuthentication.SubUser;
                                Enginelog.Info(" PSSH : SUDO SU SubAuthentication Type _serverIP: " + _server.SSHHost + " command: " + command);

                                if (!string.IsNullOrEmpty(SubAuthentication.SubPassword))
                                {
                                    terminal.Process();
                                    terminal.SendToServer(command + "\r\n");
                                    terminal.Expect(":", 10000);
                                    output = terminal.ReceivedData;
                                    Enginelog.Info(" PSSH : SUDO SU SubAuthentication Type serverIP:" + _server.SSHHost + " command: " + command + " output: " + output);

                                    terminal.Process();
                                    terminal.SendToServer(SubAuthentication.SubPassword + "\r\n");
                                    terminal.Expect(_reg, 10000);
                                    output = terminal.ReceivedData;
                                }
                                else
                                {
                                    terminal.Process();
                                    terminal.SendToServer(command + "\r\n");
                                    terminal.Expect(_reg, 10000);
                                    output = terminal.ReceivedData;

                                    Enginelog.Info(" PSSH : SUDO SU SubAuthentication Type _serverIP:" + _server.SSHHost + " command: " + command + " output: " + output);
                                }
                                Enginelog.Info(" SUDO SU SubAuthentication Type _serverIP: " + _server.SSHHost + " Completed");
                                break;
                            case (int)SubstituteAuthentication.Su:

                                command = SubAuthentication.SubPath + "su - " + SubAuthentication.SubUser;
                                Enginelog.Info(" PSSH : SU SubAuthentication Type _serverIP: " + _server.SSHHost + " command: " + command);

                                if (!string.IsNullOrEmpty(SubAuthentication.SubPassword))
                                {
                                    terminal.Process();
                                    terminal.SendToServer(command + "\r\n");
                                    terminal.Expect(":", 10000);
                                    output = terminal.ReceivedData;

                                    Enginelog.Info(" PSSH : SU SubAuthentication Type serverIP:" + _server.SSHHost + " command: " + command + " output: " + output);


                                    terminal.Process();
                                    terminal.SendToServer(SubAuthentication.SubPassword + "\r\n");
                                    terminal.Expect(_reg, 10000);
                                    output = terminal.ReceivedData;
                                }
                                else
                                {
                                    terminal.Process();
                                    terminal.SendToServer(command + "\r\n");
                                    terminal.Expect(_reg, 10000);
                                    output = terminal.ReceivedData;

                                    Enginelog.Info(" PSSH : SU SubAuthentication Type serverIP:" + _server.SSHHost + " command: " + command + " output: " + output);
                                }
                                Enginelog.Info(" SSH SU SubAuthentication Type _serverIP: " + _server.SSHHost + " Completed");
                                break;
                            case (int)SubstituteAuthentication.Sudo:
                                LoginCommand = SubAuthentication.SubPath + "sudo ";
                                break;

                            case (int)SubstituteAuthentication.Privrun:
                                LoginCommand = SubAuthentication.SubPath + "privrun ";
                                break;

                            case (int)SubstituteAuthentication.Asu:

                                command = SubAuthentication.SubPath + "asu " + SubAuthentication.SubUser;
                                Enginelog.Info(" PSSH : ASU SubAuthentication Type _serverIP: " + _server.SSHHost + " command: " + command);

                                if (!string.IsNullOrEmpty(SubAuthentication.SubPassword))
                                {
                                    terminal.Process();
                                    terminal.SendToServer(command + "\r\n");
                                    terminal.Expect(":", 10000);
                                    output = terminal.ReceivedData;

                                    Enginelog.Info(" PSSH  : ASU SubAuthentication Type serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);
                                    terminal.Process();
                                    terminal.SendToServer(SubAuthentication.SubPassword + "\r\n");
                                    terminal.Expect(_reg, 10000);
                                    output = terminal.ReceivedData;
                                }
                                else
                                {
                                    terminal.Process();
                                    terminal.SendToServer(command + "\r\n");
                                    terminal.Expect(_reg, 10000);
                                    output = terminal.ReceivedData;

                                    Enginelog.Info(" PSSH : ASU SubAuthentication Type serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);

                                }
                                Enginelog.Info(" PSSH : ASU SubAuthentication Type _serverIP: " + _server.SSHHost + " Completed");
                                break;

                            case (int)SubstituteAuthentication.Other:

                                command = SubAuthentication.SubPath + " " + SubAuthentication.SubUser;
                                Enginelog.Info(" PSSH : Other SubAuthentication Type _serverIP: " + _server.SSHHost + " command: " + command);

                                if (!string.IsNullOrEmpty(SubAuthentication.SubPassword))
                                {
                                    terminal.Process();
                                    terminal.SendToServer(command + "\r\n");
                                    terminal.Expect(":", 10000);
                                    output = terminal.ReceivedData;

                                    Enginelog.Info(" PSSH : Other SubAuthentication Type serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);
                                    terminal.Process();
                                    terminal.SendToServer(SubAuthentication.SubPassword + "\r\n");
                                    terminal.Expect(_reg, 10000);
                                    output = terminal.ReceivedData;
                                }
                                else
                                {
                                    terminal.Process();
                                    terminal.SendToServer(command + "\r\n");
                                    terminal.Expect(_reg, 10000);
                                    output = terminal.ReceivedData;

                                    Enginelog.Info(" PSSH : Other SubAuthentication Type serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);

                                }
                                Enginelog.Info(" PSSH : Other SubAuthentication Type _serverIP: " + _server.SSHHost + " Completed");
                                break;

                        }
                    }
                }
                return terminal;
            }
        }

        public dynamic ProcessDBAuthentication(string sqlCmd, string strPwd, dynamic _session, String shellPrompt)
        {
            string command = sqlCmd;
            LoginCommand = string.Empty;
            string output = string.Empty;
            VirtualTerminal terminal = null;
            string _shellPrompt = "\\$|#|>";
            Regex _reg = new Regex(_shellPrompt, RegexOptions.RightToLeft);
            if (sshConnOpt == SSHConnFlag.JSession)
            {
                if (!string.IsNullOrEmpty(strPwd))
                {
                    output = _session.SendWait(command, "password:", false, 120000);
                    Enginelog.Info(" command: " + command + " output: " + output);
                    _session.SendWait(strPwd, shellPrompt, true, 120000);
                }
                else
                {
                    output = _session.SendWait(command, shellPrompt, true, 120000);
                    Enginelog.Info(" command: " + command + " output: " + output);
                }
            }

            else
            {
                terminal = _session;

                Thread.Sleep(1000);
                terminal.Process();
                // terminal.Process();
                Thread.Sleep(1000);
                terminal.SendToServer(command + "\n");
                Thread.Sleep(1000);
                terminal.Expect("password:", 20000);
                Thread.Sleep(2000);
                string _output = terminal.ReceivedData;

                if (_output.ToLower().Contains("password:"))
                {
                    terminal.Process();
                    terminal.SendToServer(strPwd + "\n");
                    Thread.Sleep(1000);
                    terminal.SendToServer("\r\n");
                    Thread.Sleep(1000);
                    terminal.Expect(_reg, 120000);
                    Thread.Sleep(1000);
                    output = terminal.ReceivedData;
                    // Logger.Info(" Output: " + output);
                }

                //if (!string.IsNullOrEmpty(strPwd))
                //{
                //    terminal.Process();
                //    Thread.Sleep(1000);
                //    terminal.SendToServer(strPwd + "\n");
                //    Thread.Sleep(1000);
                //    terminal.SendToServer("\r\n");
                //    Thread.Sleep(1000);
                //    terminal.Expect(shellPrompt, 120000);
                //    Thread.Sleep(1000);
                //    output = terminal.ReceivedData;
                //}
                //}
                //else
                //{
                //    terminal.Expect(shellPrompt, 120000);
                //    Thread.Sleep(5000);
                //    output = terminal.ReceivedData;
                //}

            }
            return _session;
        }

        public string _ProcessDBAuthentication(string sqlCmd, string strPwd, dynamic _session, String shellPrompt)
        {
            string command = sqlCmd;
            LoginCommand = string.Empty;
            string output = string.Empty;
            VirtualTerminal terminal = null;
            string _shellPrompt = "\\$|#|:|>";
            Regex _reg = new Regex(_shellPrompt, RegexOptions.RightToLeft);
            if (sshConnOpt == SSHConnFlag.JSession)
            {
                if (!string.IsNullOrEmpty(strPwd))
                {
                    output = _session.SendWait(command, "password:", false, 120000);
                    Enginelog.Info(" command: " + command + " output: " + output);
                    _session.SendWait(strPwd, shellPrompt, true, 120000);
                }
                else
                {
                    output = _session.SendWait(command, shellPrompt, true, 120000);
                    Enginelog.Info(" command: " + command + " output: " + output);
                }
            }

            else
            {
                terminal = _session;

                Thread.Sleep(1000);
                terminal.Process();
                // terminal.Process();
                Thread.Sleep(1000);
                terminal.SendToServer(command + "\n");
                Thread.Sleep(1000);
                terminal.Expect("password:", 20000);
                Thread.Sleep(2000);
                output = terminal.ReceivedData;

                if (output.ToLower().Contains("password:"))
                {
                    terminal.Process();
                    terminal.SendToServer(strPwd + "\n");
                    Thread.Sleep(1000);
                    terminal.SendToServer("\r\n");
                    Thread.Sleep(1000);
                    terminal.Expect(_reg, 120000);
                    Thread.Sleep(1000);
                    output = terminal.ReceivedData;
                    // Logger.Info(" Output: " + output);
                }

                //if (!string.IsNullOrEmpty(strPwd))
                //{
                //    terminal.Process();
                //    Thread.Sleep(1000);
                //    terminal.SendToServer(strPwd + "\n");
                //    Thread.Sleep(1000);
                //    terminal.SendToServer("\r\n");
                //    Thread.Sleep(1000);
                //    terminal.Expect(shellPrompt, 120000);
                //    Thread.Sleep(1000);
                //    output = terminal.ReceivedData;
                //}
                //}
                //else
                //{
                //    terminal.Expect(shellPrompt, 120000);
                //    Thread.Sleep(5000);
                //    output = terminal.ReceivedData;
                //}

            }
            return output;
        }

        //copied from cp5
        public dynamic ProcessDBAuthentication(string sqlCmd, string strPwd, dynamic _session, String shellPrompt, out string output)
        {
            string command = sqlCmd;
            LoginCommand = string.Empty;
            VirtualTerminal terminal = new VirtualTerminal(80, 400);
            //string output = string.Empty;
            if (sshConnOpt == SSHConnFlag.JSession)
            {
                if (!string.IsNullOrEmpty(strPwd))
                {
                    output = _session.SendWait(command, "password:", false, 120000);
                    Enginelog.Info(" command: " + command + " output: " + output);
                    output = _session.SendWait(strPwd, shellPrompt, true, 120000);
                    return _session;
                }
                else
                {
                    output = _session.SendWait(command, shellPrompt, true, 120000);
                    Enginelog.Info(" command: " + command + " output: " + output);
                }
                //return _session;
            }
            else
            {
                //output = _session.SendWait(command, shellPrompt, true, 120000);
                //output = PSSH.ExecuteOSCmmandWithSession(_session, shellPrompt, command, 120000);

                terminal.Bind(_session);
                Thread.Sleep(1000);
                terminal.Process();
                terminal.SendToServer(command + "\r\n");
                terminal.Expect(":", 10000);
                output = terminal.ReceivedData;
                Enginelog.Info(" command: " + command + " output: " + output);
            }
            return terminal;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        private void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {

                }
            }

            _isDisposed = true;
        }

        ~SubAuthentication()
        {
            Dispose(false);
        }
    }

    #endregion SubAuthentication

    #region SSHServerInfo

    public class SSHServerInfo
    {
        public string SSHHost { get; set; }
        public string SSHUser { get; set; }
        public string SSHPass { get; set; }
        public bool AuthKey { get; set; }
        public string SSHKeyPath { get; set; }
        public string SSHKeyPassword { get; set; }
        public List<SubAuthentication> SubAuthenticationList = null;
        private int _port;
        public string SudoUser { get; set; }

        public bool IsASMGrid { get; set; }
        public string GridHomePath { get; set; }
        public string GridInstanceName { get; set; }
        public string GridUserName { get; set; }
        public string GridPassword { get; set; }

        public int SSHPort
        {

            get { return _port; }
            set
            {
                if (value == 0)
                {
                    value = 22;
                }
                _port = value;
            }
        }

        public List<SubAuthentication> _subauthenticationlist = new List<SubAuthentication>();

        public List<SubAuthentication> SubAuthenticationlist
        {
            get { return _subauthenticationlist; }
            set { _subauthenticationlist = value; }
        }

        public SSHServerInfo()
        {

        }

        public SSHServerInfo(string strSSHHost, string strSSHUser, string strSSHPass)
        {
            SSHHost = strSSHHost;
            SSHUser = strSSHUser;
            SSHPass = strSSHPass;
        }

        // without sshkey
        public SSHServerInfo(string strSSHHost, string strSSHUser, string strSSHPass, int strsshport, List<SubAuthentication> subauthenticationlist)
        {
            SSHHost = strSSHHost;
            SSHUser = strSSHUser;
            SSHPass = strSSHPass;
            AuthKey = false;
            SSHPort = strsshport;
            SubAuthenticationList = subauthenticationlist;
        }

        // with sshkey
        public SSHServerInfo(string strSSHHost, string strSSHUser, string strSSHKeyPath, string strSSHKeyPassword, int strsshport, List<SubAuthentication> subauthenticationlist)
        {
            SSHHost = strSSHHost;
            SSHUser = strSSHUser;
            AuthKey = true;
            SSHKeyPath = strSSHKeyPath;
            SSHKeyPassword = strSSHKeyPassword;
            SSHPort = strsshport;
            SubAuthenticationList = subauthenticationlist;
        }

        public SSHServerInfo(string strSSHHost, string strSSHUser, string strSSHPass, bool authkey, string strSSHKeyPath, string strSSHKeyPassword, int strsshport)
        {
            SSHHost = strSSHHost;
            SSHUser = strSSHUser;
            SSHPass = strSSHPass;
            AuthKey = authkey;
            SSHKeyPath = strSSHKeyPath;
            SSHKeyPassword = strSSHKeyPassword;
            SSHPort = strsshport;
        }

        public SSHServerInfo(string strSSHHost, string strSSHUser, string strSSHPass, bool authkey, string strSSHKeyPath, string strSSHKeyPassword, int strsshport, List<SubAuthentication> subauthenticationlist)
        {
            SSHHost = strSSHHost;
            SSHUser = strSSHUser;
            SSHPass = strSSHPass;
            AuthKey = authkey;
            SSHKeyPath = strSSHKeyPath;
            SSHKeyPassword = strSSHKeyPassword;
            SSHPort = strsshport;
            SubAuthenticationList = subauthenticationlist;
        }
    }

    #endregion SSHServerInfo

    #region SSH - bouncy castle

    public class SshUtil
    {
        public static SshConfiguration GetSshConfig()
        {
            SshConfiguration sshConfig = new SshConfiguration();
            sshConfig.ConnectionConfig.TransportConfiguration.RemoveAllCiphers();
            sshConfig.ConnectionConfig.TransportConfiguration.AddCipher(new BouncyCastleCipher.Creator("AES", "ctr", "aes128-ctr", 16, 16));
            return sshConfig;
        }
    }

    public class BouncyCastleCipher : ICipher
    {
        private readonly IBufferedCipher cipher;

        public BouncyCastleCipher(IBufferedCipher cipher)
        {
            this.cipher = cipher;
        }

        /// <summary>
        /// The cipher block length.
        /// </summary>
        public int BlockLength
        {
            get { return this.cipher.GetBlockSize(); }
        }

        /// <summary>
        /// Processes the specified data.
        /// </summary>
        /// <param name="data">the data to process</param>
        /// <returns>the processed data</returns>
        public byte[] Process(byte[] data)
        {
            return this.cipher.ProcessBytes(data);
        }

        public class Creator : ICipherCreator
        {
            private readonly string algorithmName;
            private readonly string mode;
            private readonly string sshAlgorithmName;
            private readonly int keyLength;
            private readonly int blockLength;

            public Creator(string algorithmName, string mode, string sshAlgorithmName, int keyLength, int blockLength)
            {

                this.algorithmName = algorithmName;
                this.mode = mode;
                this.sshAlgorithmName = sshAlgorithmName;
                this.keyLength = keyLength;
                this.blockLength = blockLength;
            }

            /// <summary>
            /// The cipher key length.
            /// </summary>
            public int KeyLength
            {
                get { return this.keyLength; }
            }

            /// <summary>
            /// The cipher block length.
            /// </summary>
            public int BlockLength
            {
                get { return this.blockLength; }
            }

            /// <summary>
            /// Creates a new encipher instance.
            /// </summary>
            /// <param name="keyData">the cipher key data</param>
            /// <param name="ivData">the cipher IV data</param>
            /// <returns>the created encipher</returns>
            public ICipher CreateEncipher(byte[] keyData, byte[] ivData)
            {
                IBufferedCipher cipher = CipherFor(keyData, ivData, true);
                return new BouncyCastleCipher(cipher);
            }

            /// <summary>
            /// Creates a new decipher instance.
            /// </summary>
            /// <param name="keyData">the cipher key data</param>
            /// <param name="ivData">the cipher IV data</param>
            /// <returns>the created decipher</returns>
            public ICipher CreateDecipher(byte[] keyData, byte[] ivData)
            {
                IBufferedCipher cipher = CipherFor(keyData, ivData, false);
                return new BouncyCastleCipher(cipher);
            }

            /// <summary>
            /// The creator algorithm name.
            /// </summary>
            public string Name
            {
                get { return this.sshAlgorithmName; }
            }

            private IBufferedCipher CipherFor(byte[] keyData, byte[] ivData, bool encryption)
            {
                string cipherAlgorithm = this.algorithmName + "/" + this.mode + "/NoPadding";

                IBufferedCipher cipher = CipherUtilities.GetCipher(cipherAlgorithm);
                KeyParameter keyParameter = ParameterUtilities.CreateKeyParameter(this.algorithmName, keyData);
                ParametersWithIV parameters = new ParametersWithIV(keyParameter, ivData);
                cipher.Init(encryption, parameters);

                return cipher;
            }

        }

    }

    #endregion SSH - bouncy castle

    #region PSSHMutex

    public class PSSHMutex : IDisposable
    {

        #region Variables


        public string[] val;
        public string IpAddress = string.Empty;
        public string Port = string.Empty;
        public string UserName = string.Empty;
        private static readonly ILog Enginelog = LogManager.GetLogger("BcmsEngineLog");

        public static string shellPrompt = "\\$|#|>";
        public int expectTime = 1200000;
        public Regex _reg = new Regex(shellPrompt);
        public bool _isDisposed;

        public static SSHConnFlag sshConnOpt = (SSHConnFlag)Enum.Parse(typeof(SSHConnFlag), ConfigurationManager.AppSettings["SSHConnFlag"].ToString(), true);

        #endregion Variables

        #region Session - PSSH

        #region Create/Disconnect Session Methods - PSSH

        private Jscape.Ssh.SshParameters BindSSHParameters(SSHServerInfo _remoteServer)
        {
            string keyPass = string.Empty;
            Jscape.Ssh.SshParameters _sp = new Jscape.Ssh.SshParameters();
            _sp.Hostname = _remoteServer.SSHHost;
            _sp.Username = _remoteServer.SSHUser;
            _sp.Port = _remoteServer.SSHPort;
            if (_remoteServer.AuthKey)
            {
                FileInfo _pvtKey = new FileInfo(_remoteServer.SSHKeyPath);
                if (!string.IsNullOrEmpty(_remoteServer.SSHKeyPassword))
                {
                    keyPass = _remoteServer.SSHKeyPassword;
                }
                _sp.SetPrivateKey(_pvtKey, keyPass);
                Enginelog.Info("PSSHConnMutex JSSH Parameters with SSHKey created sucessfully..." + _remoteServer.SSHHost);
            }
            else
            {
                _sp.Password = _remoteServer.SSHPass;
                Enginelog.Info("PSSHConnMutex JSSH Parameters created sucessfully..." + _remoteServer.SSHHost);
            }

            return _sp;
        }

        public dynamic CreateSSHMutexSession(SSHServerInfo objSshInfo)
        {
            return CreateSSHSessionWithRetry(objSshInfo, true);
        }

        private dynamic CreateSSHSessionWithRetry(SSHServerInfo objSshInfo, bool isMutexSession)
        {
            string strOut = string.Empty;
            dynamic client = null;
            VirtualTerminal terminal = null;
            int retryCount = 0;
            int maxRetries = 3;
            int baseDelayMs = 1000;
            bool connectionAcquired = false;

            if (objSshInfo != null)
            {
                // Try to acquire connection slot
                if (!SSHConnectionManager.TryAcquireConnection(objSshInfo.SSHHost))
                {
                    // Wait a bit and try again
                    Thread.Sleep(2000);
                    if (!SSHConnectionManager.TryAcquireConnection(objSshInfo.SSHHost))
                    {
                        throw new Exception($"Connection limit reached for host {objSshInfo.SSHHost}. Please try again later.");
                    }
                }
                connectionAcquired = true;

                try
                {
                    while (retryCount <= maxRetries)
                    {
                        try
                        {
                        switch (sshConnOpt)
                        {
                            case SSHConnFlag.JSession:
                                client = new Jscape.Ssh.Ssh();
                                break;
                            case SSHConnFlag.RSession:
                                client = new Rebex.Net.Ssh();
                                Rebex.Security.Cryptography.CryptoHelper.UseFipsAlgorithmsOnly = false;
                                // Reduced timeout from 3600000 to 60000 (1 minute) to prevent resource buildup
                                client.Timeout = 60000;
                                break;
                        }

                        Enginelog.Info("PSSHConnMutex Creating SSH Session... " + objSshInfo.SSHHost + " (Attempt " + (retryCount + 1) + ")");

                        if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            client = new Jscape.Ssh.SshSession(BindSSHParameters(objSshInfo), SshUtil.GetSshConfig());
                            client.SetShellPrompt(shellPrompt, true);

                            client.LicenseKey =
                                "SSH Factory for .NET:Single Developer:Registered User:01-01-3999:SqmHLZPj8UhKl9+/RAnmAkXkCPfv7c3fH2F1bKjYwTmH4pOyXv5shXQkj3dAJ5Njrxa9BfTEQJmrQrccH5/vc6zXuqnBN8IOzGFjKu1V7ma7M0F54tYLuMuthW6FIoPjLFFAUU6G0LEF3rSUYDWO3vb2hSWDtFOGW87lGiNE5AA=";
                            client.Connect(30000); // Reduced from 300000 to 30000 (30 seconds)
                            Enginelog.Info("PSSHConnMutex JSession created sucessfully..." + objSshInfo.SSHHost);
                        }
                        else if (sshConnOpt == SSHConnFlag.RSession)
                        {
                            Rebex.Security.Cryptography.CryptoHelper.UseFipsAlgorithmsOnly = false;
                            client.Timeout = 60000; // Reduced timeout to 1 minute

                            // Add elliptic curve support if not in mutex session
                            if (!isMutexSession)
                            {
                                AsymmetricKeyAlgorithm.Register(EllipticCurveAlgorithm.Create);
                                AsymmetricKeyAlgorithm.Register(Curve25519.Create);
                                AsymmetricKeyAlgorithm.Register(Ed25519.Create);
                            }

                            if (objSshInfo.SSHPort != 0)
                            {
                                client.Connect(objSshInfo.SSHHost, objSshInfo.SSHPort);
                            }
                            else
                            {
                                client.Connect(objSshInfo.SSHHost);
                            }

                            if (objSshInfo.AuthKey)
                            {
                                SshPrivateKey _pvtkey1 = new SshPrivateKey(objSshInfo.SSHKeyPath, objSshInfo.SSHKeyPassword);
                                client.Login(objSshInfo.SSHUser, _pvtkey1);
                            }
                            else
                            {
                                client.Login(objSshInfo.SSHUser, objSshInfo.SSHPass);
                            }

                            // Store connection info for non-mutex sessions
                            if (!isMutexSession)
                            {
                                IpAddress = client.LocalEndPoint.Address.ToString();
                                Port = client.LocalEndPoint.Port.ToString();
                                UserName = client.UserName.ToString();
                            }

                            terminal = new VirtualTerminal(isMutexSession ? 100 : 80, isMutexSession ? 500 : 400);
                            terminal.Bind(client);

                            if (!isMutexSession)
                            {
                                terminal.Expect(_reg, 5000);
                                string con_op = terminal.ReceivedData;

                                if (con_op.ToLower().Contains("your password has expired.") || con_op.ToLower().Contains("change your password immediately"))
                                {
                                    Enginelog.Info("PSSH: RSession connection Check" + con_op);
                                    throw new Exception("Server Password is Expired...");
                                }
                            }

                            client = terminal;
                            Enginelog.Info("PSSHConnMutex RSession created sucessfully..." + objSshInfo.SSHHost);
                        }

                        // If we reach here, connection was successful
                        break;
                    }
                    catch (Jscape.Ssh.SshException SshEx)
                    {
                        retryCount++;
                        if (SshEx.InnerException != null)
                            Enginelog.Error("JPSSHConnMutex INNER_SSHException: While connecting to server: " + objSshInfo.SSHHost + " (Attempt " + retryCount + ") : Messege: " + SshEx.InnerException.ToString());

                        Enginelog.Error("JPSSHConnMutex SSHException: While connecting to server: " + objSshInfo.SSHHost + " (Attempt " + retryCount + ") : Messege: " + SshEx.Message);

                        // Clean up failed client
                        if (client != null)
                        {
                            try { client.Dispose(); } catch { }
                            client = null;
                        }

                        if (retryCount > maxRetries)
                            throw SshEx;

                        // Exponential backoff delay
                        Thread.Sleep(baseDelayMs * (int)Math.Pow(2, retryCount - 1));
                    }
                    catch (Rebex.Net.SshException SshEx)
                    {
                        retryCount++;
                        if (SshEx.InnerException != null)
                            Enginelog.Error("RPSSHConnMutex INNER_SSHException: While connecting to server: " + objSshInfo.SSHHost + " (Attempt " + retryCount + ") : Messege: " + SshEx.InnerException.ToString());

                        Enginelog.Error("RPSSHConnMutex SSHException: While connecting to server: " + objSshInfo.SSHHost + " (Attempt " + retryCount + ") : Messege: " + SshEx.Message);

                        // Clean up failed client
                        if (client != null)
                        {
                            try { client.Dispose(); } catch { }
                            client = null;
                        }

                        // Check for socket error 10055 specifically
                        if (SshEx.InnerException != null && SshEx.InnerException.Message.Contains("10055"))
                        {
                            Enginelog.Error("Socket error 10055 detected - forcing garbage collection and longer delay");
                            GC.Collect();
                            GC.WaitForPendingFinalizers();
                            Thread.Sleep(5000); // Longer delay for socket exhaustion
                        }

                        if (retryCount > maxRetries)
                            throw SshEx;

                        // Exponential backoff delay
                        Thread.Sleep(baseDelayMs * (int)Math.Pow(2, retryCount - 1));
                    }
                    catch (Exception exc)
                    {
                        retryCount++;
                        if (exc.InnerException != null)
                            Enginelog.Error("PSSHConnMutex INNER_Exception: While connecting to server: " + objSshInfo.SSHHost + " (Attempt " + retryCount + ") : Messege: " + exc.InnerException.Message.ToString());

                        Enginelog.Error("PSSHConnMutex Exception: While connecting to server: " + objSshInfo.SSHHost + " (Attempt " + retryCount + ") : Messege: " + exc.Message);

                        // Clean up failed client
                        if (client != null)
                        {
                            try { client.Dispose(); } catch { }
                            client = null;
                        }

                        // Check for socket error 10055 specifically
                        if (exc.Message.Contains("10055") || (exc.InnerException != null && exc.InnerException.Message.Contains("10055")))
                        {
                            Enginelog.Error("Socket error 10055 detected - forcing garbage collection and longer delay");
                            GC.Collect();
                            GC.WaitForPendingFinalizers();
                            Thread.Sleep(5000); // Longer delay for socket exhaustion
                        }

                        if (retryCount > maxRetries)
                            throw exc;

                        // Exponential backoff delay
                        Thread.Sleep(baseDelayMs * (int)Math.Pow(2, retryCount - 1));
                    }
                }
                catch (Exception)
                {
                    // Release connection on any unhandled exception
                    if (connectionAcquired)
                    {
                        SSHConnectionManager.ReleaseConnection(objSshInfo.SSHHost);
                        connectionAcquired = false;
                    }
                    throw;
                }
            }
            else
            {
                Enginelog.Error("PSSHConnMutex SSHSERVER NULL While Create SSH Session for SSHFlag: " + sshConnOpt.ToString());
                if (connectionAcquired)
                {
                    SSHConnectionManager.ReleaseConnection(objSshInfo.SSHHost);
                }
            }
            return client;
        }

        public dynamic CreateSSHSession(SSHServerInfo objSshInfo)
        {
            return CreateSSHSessionWithRetry(objSshInfo, false);
        }





        public bool DisconnectAndRemoveSSHSession(dynamic client)
        {
            return DisconnectAndRemoveSSHSession(client, null);
        }

        public bool DisconnectAndRemoveSSHSession(dynamic client, string host)
        {
            bool result = false;

            if (client != null)
            {
                try
                {
                    switch (sshConnOpt)
                    {
                        case SSHConnFlag.JSession:
                            {
                                try
                                {
                                    if (client != null)
                                    {
                                        client.Disconnect();
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Enginelog.Error("Error disconnecting JSession: " + ex.Message);
                                }
                            }
                            break;
                        case SSHConnFlag.RSession:
                            {
                                try
                                {
                                    if (client != null && !client.IsDisposed)
                                    {
                                        // Try graceful exit first
                                        try
                                        {
                                            Regex _reg = new Regex(shellPrompt, RegexOptions.RightToLeft);
                                            client.Process();
                                            client.SendToServer("exit" + "\r\n");
                                            client.Expect(_reg, 2000); // Reduced timeout from 5000 to 2000
                                            Thread.Sleep(500); // Reduced sleep from 1000 to 500
                                        }
                                        catch (Exception ex)
                                        {
                                            Enginelog.Error("Error during graceful exit: " + ex.Message);
                                        }

                                        // Unbind the terminal
                                        try
                                        {
                                            client.Unbind();
                                        }
                                        catch (Exception ex)
                                        {
                                            Enginelog.Error("Error unbinding terminal: " + ex.Message);
                                        }
                                    }

                                    // Force disposal
                                    if (client != null && !client.IsDisposed)
                                    {
                                        try
                                        {
                                            client.Dispose();
                                        }
                                        catch (Exception ex)
                                        {
                                            Enginelog.Error("Error disposing client: " + ex.Message);
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Enginelog.Error("Error in RSession cleanup: " + ex.Message);
                                    // Force disposal even if other operations failed
                                    try
                                    {
                                        if (client != null && !client.IsDisposed)
                                        {
                                            client.Dispose();
                                        }
                                    }
                                    catch { }
                                }
                            }
                            break;
                    }

                    // Clear connection info
                    IpAddress = "";
                    Port = "";

                    // Release connection from pool if host is provided
                    if (!string.IsNullOrEmpty(host))
                    {
                        SSHConnectionManager.ReleaseConnection(host);
                    }

                    result = true;
                    Enginelog.Info("Session Disconnected successfully...");
                }
                catch (Jscape.Ssh.SshException SshEx)
                {
                    if (SshEx.InnerException != null)
                        Enginelog.Error("JPSSHConnMutex: INNER_SSHException: on DisconnectAndRemoveSSHSession: Jscape SshEx InnerException: " + SshEx.InnerException.ToString());

                    Enginelog.Error("JPSSHConnMutex: SSHException: on DisconnectAndRemoveSSHSession: Jscape SshEx Exception: " + SshEx.Message);
                }
                catch (Rebex.Net.SshException SshEx)
                {
                    if (SshEx.InnerException != null)
                        Enginelog.Error("RPPSSHConnMutex: INNER_SSHException: on DisconnectAndRemoveSSHSession: Rebex SshEx InnerException: " + SshEx.InnerException.ToString());

                    Enginelog.Error("RPSSHConnMutex: SSHException: on DisconnectAndRemoveSSHSession: Rebex SshEx Exception: " + SshEx.Message);
                }
                catch (Exception exc)
                {
                    if (exc.InnerException != null)
                        Enginelog.Error("PSSHConnMutex: INNER_Exception: on DisconnectAndRemoveSSHSession:  InnerException: " + exc.InnerException.Message.ToString());

                    Enginelog.Error("PSSHConnMutex: Exception: on DisconnectAndRemoveSSHSession: Exception: " + exc.Message);
                }
            }
            else
            {
                Enginelog.Error("PSSHConnMutex: client is null on DisconnectAndRemoveSSHSession for: " + sshConnOpt.ToString());
            }
            return result;
        }
        public bool KillZombieProcess(SSHServerInfo objSshInfo)
        {
            bool removeConn = false;
            dynamic client = null;

            try
            {
                client = CreateSSHSession(objSshInfo);
                removeConn = CheckZombieAndClear(client, objSshInfo.SSHHost);
                removeConn = DisconnectAndRemoveSSHSession(client);
            }
            catch (Rebex.Net.SshException SshEx)
            {
                if (SshEx.InnerException != null)
                {
                    Enginelog.Error("PSSHConnMutex: INNER_SSHException: on ConnectServer: Rebex SshEx InnerException: " + SshEx.InnerException.Message.ToString());
                }
                Enginelog.Error("PSSHConnMutex: SSHException: on ConnectServer: Rebex SshEx Exception: " + SshEx.Message);
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    Enginelog.Error("PSSHConnMutex: INNER_Exception: on ConnectServer:  InnerException: " + exc.InnerException.Message.ToString());
                }

                Enginelog.Error("PSSHConnMutex: Exception: on ConnectServer: Exception: " + exc.Message);
            }
            return removeConn;
        }

        public bool ConnectServer(SSHServerInfo objSshInfo)
        {
            bool removeConn = false;
            dynamic client = null;

            try
            {
                client = CreateSSHSession(objSshInfo);
                removeConn = CheckZombieAndClear(client, objSshInfo.SSHHost);
                removeConn = DisconnectAndRemoveSSHSession(client);
            }
            catch (Rebex.Net.SshException SshEx)
            {
                if (SshEx.InnerException != null)
                    Enginelog.Error("PSSHConnMutex: INNER_SSHException: on ConnectServer: Rebex SshEx InnerException: " + SshEx.InnerException.Message.ToString());

                Enginelog.Error("PSSHConnMutex: SSHException: on ConnectServer: Rebex SshEx Exception: " + SshEx.Message);
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    Enginelog.Error("PSSHConnMutex: INNER_Exception: on ConnectServer:  InnerException: " + exc.InnerException.Message.ToString());

                Enginelog.Error("PSSHConnMutex: Exception: on ConnectServer: Exception: " + exc.Message);
            }

            return removeConn;
        }

        public bool CheckZombieAndClear(dynamic client, string ipaddress)
        {
            bool result = false;
            try
            {
                List<string> val = new List<string>();
                string strOutdb = string.Empty;
                string shellPrompt = "\\$|#|]|>";
                int zombiePrcKt = 0;
                int totalZombie = 0;
                string strOut = string.Empty;

                Enginelog.Info("CheckZombieAndClear: Server Name: " + ipaddress);

                string outStr = ExecuteOSCmmandWithSession(client, shellPrompt, "ps -eo pid,ppid,stat,comm | grep Z");
                if (outStr != null)
                {
                    string[] stringSeparators = new string[] { "\r\n" };
                    string[] rows = outStr.Split(stringSeparators, StringSplitOptions.None);

                    if (rows != null && rows.Count() > 0)
                    {
                        foreach (string zpr in rows)
                        {
                            char[] delimiterChars = { ' ', '\t' };
                            string[] cols = zpr.Split(delimiterChars, StringSplitOptions.None);
                            if (cols != null && cols.Count() > 1)
                            {
                                string ppid = cols[1];
                                try
                                {
                                    int pprcId = Int32.Parse(ppid);
                                    totalZombie++;
                                    strOut = ExecuteOSCmmandWithSession(client, shellPrompt, "kill -9 " + ppid);
                                    if (!string.IsNullOrEmpty(strOut) && !strOut.Contains("Exception"))
                                    {
                                        zombiePrcKt++;
                                    }
                                    else
                                    {
                                        Enginelog.Info("CheckZombieAndClear: exception raised for Server: " + ipaddress + " and the exception: " + strOut);
                                    }
                                }
                                catch (FormatException)
                                {
                                    Enginelog.Info("CheckZombieAndClear: Unable to parse " + ppid + " into process Id (int format)");
                                }
                            }
                        }
                        Enginelog.Info("CheckZombieAndClear: Total zombieProcesses found count: " + totalZombie.ToString());
                        Enginelog.Info("CheckZombieAndClear: the cleared zombieProcesses count: " + zombiePrcKt.ToString());
                    }
                    else
                    {
                        Enginelog.Info("CheckZombieAndClear: there is no Zombie Count found for Server: " + ipaddress);
                    }
                }
                result = true;
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    Enginelog.Error("PSSHConnMutex: in CheckZombieAndClear, InnerException: " + exc.InnerException.Message.ToString());
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex: in CheckZombieAndClear, Exception: " + exc.Message.ToString());
                }
            }

            return result;
        }

        public bool SessionDisconnect(dynamic client, string command, string prompt, string ipaddress)
        {
            try
            {
                List<string> val = new List<string>();
                string strOutdb = string.Empty;
                string shellPrompt = "\\$|#|]|>";

                //strOutdb = PSSH.ExecuteDBCommandWithSession(client, command, 600000, shellPrompt);
                ////strOutdb = PSSH.ExecuteOSCmmandWithSession(client, shellPrompt, command, 600000);
                //return strOutdb;
                Enginelog.Info("PSSHConnMutex Server Name : " + prompt);

                if (prompt == "root")
                {
                    string out1 = ExecuteOSCmmandWithSession(client, shellPrompt, "netstat -plant | grep " + ipaddress);
                    if (out1 != null)
                    {
                        string[] stringSeparators = new string[] { "\r\n" };
                        string[] lines = out1.Split(stringSeparators, StringSplitOptions.None);
                        foreach (string v in lines)
                        {
                            if (v.Contains(ipaddress))
                            {
                                string[] stringSeparators1 = new string[] { "ESTABLISHED" };
                                string[] stringSeparators2 = new string[] { "/" };
                                string[] l1 = v.Split(stringSeparators1, StringSplitOptions.None);
                                string[] l2 = l1[1].Split(stringSeparators2, StringSplitOptions.None);
                                string v2 = l2[0];
                                val.Add(v2);
                            }
                        }
                        foreach (var v1 in val)
                        {
                            string out2 = ExecuteOSCmmandWithSession(client, shellPrompt, "kill " + v1);
                        }
                    }
                }
                else
                {
                    //string out1 = PSSH.ExecuteOSCmmandWithSession(client, shellPrompt, "ps -U " + prompt + " | grep ssh");

                    //if (out1 != null)
                    //{
                    //    string[] stringSeparators = new string[] { "\r\n" };
                    //    string[] lines = out1.Split(stringSeparators, StringSplitOptions.None);
                    //    foreach (string v in lines)
                    //    {
                    //        if (v.Contains("?"))
                    //        {
                    //            string[] stringSeparators1 = new string[] { "?" };
                    //            string[] l1 = v.Split(stringSeparators1, StringSplitOptions.None);
                    //            string v2 = l1[0];
                    //            val.Add(v2);
                    //        }
                    //    }
                    //}
                    //foreach (var v1 in val)
                    //{
                    //    string out2 = PSSH.ExecuteOSCmmandWithSession(client, shellPrompt, "kill " + v1);
                    //}
                }


            }

#pragma warning disable CS0168 // The variable 'ex' is declared but never used
            catch (Exception ex)
#pragma warning restore CS0168 // The variable 'ex' is declared but never used
            {

            }

            return true;
        }


        #endregion Create Session - PSSHMutex

        #region Common Execution methods - PSSHMutex

        public string ExecuteOSCommand(SSHServerInfo objSshInfo, string shellPrompt, string command)
        {
            string strOut = string.Empty;
            strOut = ExecuteOSCommand(objSshInfo, shellPrompt, command, expectTime);
            return strOut;
        }

        public string ExecuteOSCommand(SSHServerInfo objSshInfo, string shellPrompt, string command, int timeout)
        {
            string strOut = string.Empty;
            //Regex _reg = new Regex(shellPrompt);
            Regex _reg = new Regex(shellPrompt, RegexOptions.RightToLeft);
            dynamic client = null;
            VirtualTerminal terminal = null;
            try
            {
                //client = CreateSSHSession(objSshInfo);
                client = CreateSSHMutexSession(objSshInfo);
                if (client != null)
                {
                    if (sshConnOpt == SSHConnFlag.JSession)
                    {
                        client.SetShellPrompt(shellPrompt, true);
                        strOut = client.SendWait(command, shellPrompt, true, timeout);
                    }
                    else if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        //terminal = new VirtualTerminal(80, 400);
                        //terminal.Bind(client);

                        terminal = client;
                        Thread.Sleep(1000);
                        terminal.Process();
                        terminal.SendToServer(command + "\n");
                        terminal.Expect(_reg, timeout);
                        Thread.Sleep(2000);
                        strOut = terminal.ReceivedData;
                        //string pmt =GetDynamicPromt(strOut);
                    }
                    Enginelog.Info("PSSHConnMutex Command(in ExecuteOSCmmand): " + command + " executed successfully");
                }

            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCmmand(with server conn details), InnerException: " + exc.InnerException.Message.ToString());
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCmmand(with server conn details), Exception: " + exc.Message.ToString());
                }
            }
            finally
            {
                //if (sshConnOpt == SSHConnFlag.RSession)
                //{
                //    if (terminal != null)
                //    {
                //        terminal.Unbind();
                //    }
                //    if (!terminal.IsDisposed)
                //    {
                //        terminal.Dispose();
                //    }
                //}
                DisconnectAndRemoveSSHSession(client);
            }

            return strOut;
        }

        public string ExecuteOSCmmand(SSHServerInfo objSshInfo, string shellPrompt, string command)
        {
            string strOut = string.Empty;
            strOut = ExecuteOSCmmand(objSshInfo, shellPrompt, command, expectTime);
            return strOut;
        }

        public string ExecuteOSCmmand(SSHServerInfo objSshInfo, string shellPrompt, string command, int timeout)
        {
            string strOut = string.Empty;
            //Regex _reg = new Regex(shellPrompt);
            Regex _reg = new Regex(shellPrompt, RegexOptions.RightToLeft);
            dynamic client = null;
            VirtualTerminal terminal = null;
            try
            {
                client = CreateSSHSession(objSshInfo);

                if (client != null)
                {
                    if (sshConnOpt == SSHConnFlag.JSession)
                    {
                        client.SetShellPrompt(shellPrompt, true);
                        strOut = client.SendWait(command, shellPrompt, true, timeout);
                    }
                    else if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        //terminal = new VirtualTerminal(80, 400);
                        //terminal.Bind(client);

                        terminal = client;
                        Thread.Sleep(1000);
                        terminal.Process();
                        terminal.SendToServer(command + "\n");
                        terminal.Expect(_reg, timeout);
                        Thread.Sleep(2000);
                        strOut = terminal.ReceivedData;
                        //string pmt =GetDynamicPromt(strOut);
                    }
                    Enginelog.Info("PSSHConnMutex Command(in ExecuteOSCmmand): " + command + " executed successfully");
                }

            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCmmand(with server conn details), InnerException: " + exc.InnerException.Message.ToString());
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCmmand(with server conn details), Exception: " + exc.Message.ToString());
                }
            }
            finally
            {
                //if (sshConnOpt == SSHConnFlag.RSession)
                //{
                //    if (terminal != null)
                //    {
                //        terminal.Unbind();
                //    }
                //    if (!terminal.IsDisposed)
                //    {
                //        terminal.Dispose();
                //    }
                //}
                DisconnectAndRemoveSSHSession(client);
            }

            return strOut;
        }

        public string ExecuteOSCommandWithSubAuth(SSHServerInfo objSshInfo, string shellPrompt, string command)
        {
            string strOut = string.Empty;
            strOut = ExecuteOSCommandWithSubAuth(objSshInfo, shellPrompt, command, expectTime);
            return strOut;
        }

        public string ExecuteOSCommandWithSubAuth(SSHServerInfo objSshInfo, string shellPrompt, string command, int timeout)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrompt);
            dynamic client = null;
            VirtualTerminal terminal = null;
            try
            {
                client = CreateSSHSession(objSshInfo);

                if (client != null)
                {
                    using (SubAuthentication subAuth = new SubAuthentication())
                    {
                        client = subAuth.ProcessSubAuthentication(objSshInfo, client, shellPrompt);
                    }

                    if (sshConnOpt == SSHConnFlag.JSession)
                    {
                        client.SetShellPrompt(shellPrompt, true);
                        strOut = client.SendWait(command, shellPrompt, true, timeout);
                    }
                    else if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        //terminal = new VirtualTerminal(80, 400);
                        //terminal.Bind(client);
                        terminal = client;

                        terminal.Process();
                        Thread.Sleep(1000);
                        terminal.SendToServer(command + "\n");
                        terminal.Expect(_reg, timeout);
                        Thread.Sleep(3000);
                        strOut = terminal.ReceivedData;
                    }
                    Enginelog.Info("PSSHConnMutex Command(in ExecuteOSCmmand): " + command + " executed successfully");
                }

            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCmmand(with server conn details), InnerException: " + exc.InnerException.Message.ToString());
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCmmand(with server conn details), Exception: " + exc.Message.ToString());
                }
            }
            finally
            {
                if (sshConnOpt == SSHConnFlag.RSession)
                {
                    if (terminal != null)
                    {
                        terminal.Unbind();
                    }
                    if (!terminal.IsDisposed)
                    {
                        terminal.Dispose();
                    }
                }
                DisconnectAndRemoveSSHSession(client);
            }

            return strOut;
        }

        public string ExecuteOSCommands_vSphere(SSHServerInfo objSshInfo, string shellPrompt, string[] commands)
        {
            string strOut = string.Empty;
            strOut = ExecuteOSCommands_vSphere(objSshInfo, shellPrompt, commands, expectTime);
            return strOut;
        }

        public string ExecuteOSCommands_vSphere(SSHServerInfo objSshInfo, string shellPrompt, string[] commands, int timeout)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrompt);
            dynamic client = null;
            VirtualTerminal terminal = null;
            try
            {
                client = CreateSSHSession(objSshInfo);

                if (client != null)
                {
                    for (int i = 0; i < commands.Length; i++)
                    {
                        string command = commands[i];

                        if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            client.SetShellPrompt(shellPrompt, true);
                            strOut = client.SendWait(command, shellPrompt, true, timeout);
                        }
                        else if (sshConnOpt == SSHConnFlag.RSession)
                        {
                            //terminal = new VirtualTerminal(80, 400);
                            //terminal.Bind(client);

                            terminal = client;

                            terminal.Process();
                            Thread.Sleep(1000);
                            terminal.Process();
                            terminal.SendToServer(command + "\r\n");
                            Thread.Sleep(2000);
                            //terminal.Expect("]", timeout);
                            terminal.Expect("&", 2000);
                            //   Thread.Sleep(2000);
                            strOut = terminal.ReceivedData;
                        }
                        Enginelog.Info("PSSHConnMutex Command(in ExecuteOSCommands): " + command + " executed successfully");
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCommands(with server conn details), InnerException: " + exc.InnerException.Message.ToString());
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCommands(with server conn details), Exception: " + exc.Message.ToString());
                }
            }
            finally
            {
                //if (sshConnOpt == SSHConnFlag.RSession)
                //{
                //    if (terminal != null)
                //    {
                //        terminal.Unbind();
                //    }
                //    if (!terminal.IsDisposed)
                //    {
                //        terminal.Dispose();
                //    }
                //}
                DisconnectAndRemoveSSHSession(client);
            }

            return strOut;
        }

        public string ExecuteOSCommands_vSphereTarget(SSHServerInfo objSshInfo, string shellPrompt, string[] commands)
        {
            string strOut = string.Empty;
            strOut = ExecuteOSCommands_vSphereTarget(objSshInfo, shellPrompt, commands, expectTime);
            return strOut;
        }

        public string ExecuteOSCommands_vSphereTarget(SSHServerInfo objSshInfo, string shellPrompt, string[] commands, int timeout)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrompt);
            dynamic client = null;
            VirtualTerminal terminal = null;
            try
            {
                client = CreateSSHSession(objSshInfo);

                if (client != null)
                {
                    for (int i = 0; i < commands.Length; i++)
                    {
                        string command = commands[i];

                        if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            client.SetShellPrompt(shellPrompt, true);
                            strOut = client.SendWait(command, shellPrompt, true, timeout);
                        }
                        else if (sshConnOpt == SSHConnFlag.RSession)
                        {
                            //terminal = new VirtualTerminal(80, 400);
                            //terminal.Bind(client);

                            terminal = client;

                            terminal.Process();
                            Thread.Sleep(1000);
                            terminal.Process();
                            terminal.SendToServer(command + "\r\n");
                            Thread.Sleep(2000);
                            // terminal.Expect("]", timeout);
                            terminal.Expect("&", 2000);
                            //   Thread.Sleep(2000);
                            strOut = terminal.ReceivedData;
                        }
                        Enginelog.Info("PSSHConnMutex Command(in ExecuteOSCommands): " + command + " executed successfully");
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCommands(with server conn details), InnerException: " + exc.InnerException.Message.ToString());
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCommands(with server conn details), Exception: " + exc.Message.ToString());
                }
            }
            finally
            {
                //if (sshConnOpt == SSHConnFlag.RSession)
                //{
                //    if (terminal != null)
                //    {
                //        terminal.Unbind();
                //    }
                //    if (!terminal.IsDisposed)
                //    {
                //        terminal.Dispose();
                //    }
                //}
                DisconnectAndRemoveSSHSession(client);
            }

            return strOut;
        }

        public string ExecuteOSCommands(SSHServerInfo objSshInfo, string shellPrompt, string[] commands)
        {
            string strOut = string.Empty;
            strOut = ExecuteOSCommands(objSshInfo, shellPrompt, commands, expectTime);
            return strOut;
        }

        public string ExecuteOSCommands(SSHServerInfo objSshInfo, string shellPrompt, string[] commands, int timeout)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrompt);
            dynamic client = null;
            VirtualTerminal terminal = null;
            try
            {
                client = CreateSSHSession(objSshInfo);

                if (client != null)
                {
                    for (int i = 0; i < commands.Length; i++)
                    {
                        string command = commands[i];

                        if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            client.SetShellPrompt(shellPrompt, true);
                            strOut = client.SendWait(command, shellPrompt, true, timeout);
                        }
                        else if (sshConnOpt == SSHConnFlag.RSession)
                        {
                            //terminal = new VirtualTerminal(80, 400);
                            //terminal.Bind(client);

                            terminal = client;
                            Thread.Sleep(1000);
                            terminal.Process();
                            terminal.SendToServer(command + "\n");
                            terminal.Expect(_reg, timeout);
                            Thread.Sleep(2000);
                            strOut = terminal.ReceivedData;
                        }
                        Enginelog.Info("PSSHConnMutex Command(in ExecuteOSCommands): " + command + " executed successfully");
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCommands(with server conn details), InnerException: " + exc.InnerException.Message.ToString());
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCommands(with server conn details), Exception: " + exc.Message.ToString());
                }
            }
            finally
            {
                //if (sshConnOpt == SSHConnFlag.RSession)
                //{
                //    if (terminal != null)
                //    {
                //        terminal.Unbind();
                //    }
                //    if (!terminal.IsDisposed)
                //    {
                //        terminal.Dispose();
                //    }
                //}
                DisconnectAndRemoveSSHSession(client);
            }

            return strOut;
        }

        public string ExecuteOSCommandsWithSubAuth(SSHServerInfo objSshInfo, string shellPrompt, string[] commands)
        {
            string strOut = string.Empty;
            strOut = ExecuteOSCommandsWithSubAuth(objSshInfo, shellPrompt, commands, expectTime);
            return strOut;
        }

        public string ExecuteOSCommandsWithSubAuth(SSHServerInfo objSshInfo, string shellPrompt, string[] commands, int timeout)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrompt);
            dynamic client = null;
            VirtualTerminal terminal = null;
            try
            {
                client = CreateSSHSession(objSshInfo);

                if (client != null)
                {
                    using (SubAuthentication subAuth = new SubAuthentication())
                    {
                        client = subAuth.ProcessSubAuthentication(objSshInfo, client, shellPrompt);
                    }

                    for (int i = 0; i < commands.Length; i++)
                    {
                        string command = commands[i];

                        if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            client.SetShellPrompt(shellPrompt, true);
                            strOut = client.SendWait(command, shellPrompt, true, timeout);
                        }
                        else if (sshConnOpt == SSHConnFlag.RSession)
                        {
                            //terminal = new VirtualTerminal(80, 400);
                            //terminal.Bind(client);
                            terminal = client;

                            terminal.Process();
                            Thread.Sleep(1000);
                            terminal.SendToServer(command + "\n");
                            terminal.Expect(_reg, timeout);
                            Thread.Sleep(3000);
                            strOut = terminal.ReceivedData;
                        }
                        Enginelog.Info("PSSHConnMutex Command(in ExecuteOSCommandsWithSubAuth): " + command + " executed successfully");
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCommandsWithSubAuth(with server conn details), InnerException: " + exc.InnerException.Message.ToString());
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCommandsWithSubAuth(with server conn details), Exception: " + exc.Message.ToString());
                }
            }
            finally
            {
                if (sshConnOpt == SSHConnFlag.RSession)
                {
                    if (terminal != null)
                    {
                        terminal.Unbind();
                    }
                    if (!terminal.IsDisposed)
                    {
                        terminal.Dispose();
                    }
                }
                DisconnectAndRemoveSSHSession(client);
            }

            return strOut;
        }

        public string ExecuteOSCmmandWithSessionMysql(dynamic client, string shellPrompt, string command, string OStype)
        {
            string strOut = string.Empty;

            strOut = ExecuteOSCmmandWithSessionmysql(client, shellPrompt, command, expectTime, OStype);
            return strOut;
        }

        public string ExecuteOSCmmandWithSessionmysql(dynamic client, string shellPrompt, string command, int timeout, string OStype)
        {
            string strOut = string.Empty;
            //Regex _reg = new Regex(shellPrompt);
            VirtualTerminal terminal = null;
            Regex _reg = new Regex(shellPrompt, RegexOptions.RightToLeft);
            string _strOut = string.Empty;
            //string s="\u000D\u000A";

            try
            {
                //Enginelog.Info("PSSHConnMutex ExecuteOSCmmandWithSession executing command: " + command);
                if (client != null)
                {
                    if (sshConnOpt == SSHConnFlag.JSession)
                    {
                        // client.SetShellPrompt(shellPrompt, true);
                        strOut = client.SendWait(command, shellPrompt, true, timeout);
                    }
                    else if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        //terminal = new VirtualTerminal(80, 400);
                        //terminal.Bind(client);

                        terminal = client;
                        terminal.Process();
                        Thread.Sleep(1000);
                        //terminal.Process();
                        //Thread.Sleep(1000);
                        terminal.SendToServer(command + "\n");
                        if (OStype.ToLower().Contains("Windows"))
                        {
                            Thread.Sleep(1000);
                            terminal.SendToServer("\r\n");
                        }
                        Thread.Sleep(1000);
                        terminal.Expect(_reg, timeout);
                        Thread.Sleep(3000);
                        strOut = terminal.ReceivedData;
                    }


                    if (strOut.Contains(command))
                    {
                        _strOut = strOut.Replace(command + "\r\n", "");
                        strOut = _strOut;
                    }


                }
                // Enginelog.Info("PSSHConnMutex ExecuteOSCmmandWithSession executing command completed: " + command + " Output: " + strOut);
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCmmand(with dynamic conn object), InnerException: " + exc.InnerException.Message.ToString());
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCmmand(with dynamic conn object), Exception: " + exc.Message.ToString());
                }
            }

            //finally
            //{
            //    if (sshConnOpt == SSHConnFlag.RSession)
            //    {
            //        if (terminal != null)
            //        {
            //            terminal.Unbind();
            //        }
            //        if (!terminal.IsDisposed)
            //        {
            //            terminal.Dispose();
            //        }
            //    }
            //}

            return strOut;
        }

        public string ExecuteOSCmmandWithSession(dynamic client, string shellPrompt, string command)
        {
            string strOut = string.Empty;

            strOut = ExecuteOSCmmandWithSession(client, shellPrompt, command, expectTime);
            return strOut;
        }

        public string ExecuteOSCmmandWithSession(dynamic client, string shellPrompt, string command, int timeout)
        {
            string strOut = string.Empty;
            //Regex _reg = new Regex(shellPrompt);
            VirtualTerminal terminal = null;
            Regex _reg = new Regex(shellPrompt, RegexOptions.RightToLeft);
            string _strOut = string.Empty;
            //string s="\u000D\u000A";

            try
            {
                //Enginelog.Info("PSSHConnMutex ExecuteOSCmmandWithSession executing command: " + command);
                if (client != null)
                {
                    if (sshConnOpt == SSHConnFlag.JSession)
                    {
                        // client.SetShellPrompt(shellPrompt, true);
                        strOut = client.SendWait(command, shellPrompt, true, timeout);
                    }
                    else if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        //terminal = new VirtualTerminal(80, 400);
                        //terminal.Bind(client);

                        terminal = client;
                        terminal.Process();
                        Thread.Sleep(1000);
                        //terminal.Process();
                        //Thread.Sleep(1000);
                        terminal.SendToServer(command + "\n");
                        Thread.Sleep(1000);
                        terminal.SendToServer("\r\n");
                        Thread.Sleep(1000);
                        terminal.Expect(_reg, timeout);
                        Thread.Sleep(3000);
                        strOut = terminal.ReceivedData;
                    }

                    if (strOut.Contains(command))
                    {
                        _strOut = strOut.Replace(command + "\r\n", "");
                        strOut = _strOut;
                    }
                }
                // Enginelog.Info("PSSHConnMutex ExecuteOSCmmandWithSession executing command completed: " + command + " Output: " + strOut);
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCmmand(with dynamic conn object), InnerException: " + exc.InnerException.Message.ToString());
                }

                Enginelog.Error("PSSHConnMutex While ExecuteOSCmmand(with dynamic conn object), Exception: " + exc.Message.ToString());
                strOut = "Exception in PSSHConnMutex While ExecuteOSCmmand(with dynamic conn object): " + exc.Message.ToString();
            }

            //finally
            //{
            //    if (sshConnOpt == SSHConnFlag.RSession)
            //    {
            //        if (terminal != null)
            //        {
            //            terminal.Unbind();
            //        }
            //        if (!terminal.IsDisposed)
            //        {
            //            terminal.Dispose();
            //        }
            //    }
            //}

            return strOut;
        }

        public string ExecuteOSCmmandsWithSession(dynamic client, string shellPrompt, string[] commands)
        {
            string strOut = string.Empty;
            strOut = ExecuteOSCmmandsWithSession(client, shellPrompt, commands, expectTime);
            return strOut;
        }

        public string ExecuteOSCmmandsWithSession(dynamic client, string shellPrompt, string[] commands, int timeout)
        {
            string strOut = string.Empty;
            //Regex _reg = new Regex(shellPrompt);
            VirtualTerminal terminal = null;
            Regex _reg = new Regex(shellPrompt, RegexOptions.RightToLeft);
            try
            {
                //Enginelog.Info("PSSHConnMutex ExecuteOSCmmandWithSession executing command: " + command);
                if (client != null)
                {
                    for (int i = 0; i < commands.Length; i++)
                    {
                        string command = commands[i];
                        if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            // client.SetShellPrompt(shellPrompt, true);
                            strOut = client.SendWait(commands[i], shellPrompt, true, timeout);
                        }
                        else if (sshConnOpt == SSHConnFlag.RSession)
                        {
                            //terminal = new VirtualTerminal(80, 400);
                            //terminal.Bind(client);

                            terminal = client;
                            terminal.Process();
                            Thread.Sleep(1000);
                            //terminal.Process();
                            terminal.SendToServer(command + "\n");
                            Thread.Sleep(1000);
                            terminal.SendToServer("\r\n");
                            Thread.Sleep(1000);
                            terminal.Expect(_reg, timeout);
                            Thread.Sleep(3000);
                            strOut = terminal.ReceivedData;
                        }
                    }
                }
                // Enginelog.Info("PSSHConnMutex ExecuteOSCmmandWithSession executing command completed: " + command + " Output: " + strOut);
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCmmand(with dynamic conn object), InnerException: " + exc.InnerException.Message.ToString());
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCmmand(with dynamic conn object), Exception: " + exc.Message.ToString());
                }
            }

            //finally
            //{
            //    if (sshConnOpt == SSHConnFlag.RSession)
            //    {
            //        if (terminal != null)
            //        {
            //            terminal.Unbind();
            //        }
            //        if (!terminal.IsDisposed)
            //        {
            //            terminal.Dispose();
            //        }
            //    }
            //}

            return strOut;
        }

        public string ExecuteDBCommands(SSHServerInfo objSshInfo, string[] strCommand, string shellPrpt)
        {
            string strOut = string.Empty;
            strOut = ExecuteDBCommands(objSshInfo, strCommand, expectTime, shellPrpt);
            return strOut;
        }

        public string ExecuteDBCommands(SSHServerInfo objSshInfo, string[] strCommand, int waitTime, string shellPrpt)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrpt);
            dynamic client = null;
            VirtualTerminal terminal = null;
            string[] _strOuts = new string[strCommand.Length];

            shellPrompt = shellPrpt;
            expectTime = waitTime;

            try
            {
                int _connectCount = 0;
                while (true)
                {
                    try
                    {
                        client = CreateSSHSession(objSshInfo);

                        if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            if (client.Ssh.Connected)
                            {
                                Enginelog.Info(" PSSHConnMutex: ExecuteDBCommands Server: " + objSshInfo.SSHHost.ToString() + " JSession Connected ...!");
                                break;
                            }
                        }
                        else
                        {
                            if (!client.IsDisposed)
                            {
                                Enginelog.Info(" PSSHConnMutex: ExecuteDBCommands Server: " + objSshInfo.SSHHost.ToString() + " RSession Connected ...!");
                                break;
                            }
                            //if (client.IsConnected)
                            //{
                            //    Enginelog.Info(" PSSHConnMutex: ExecuteDBCommands Server: " + objSshInfo.SSHHost.ToString() + " RSession Connected ...!");
                            //    break;
                            //}
                        }

                        Thread.Sleep(5000);

                    }
                    catch (Exception _ex)
                    {
                        _connectCount++;
                        Thread.Sleep(5000);

                        Enginelog.Error(objSshInfo.SSHHost + " : ExecuteDBCommands Connection Failed _connectCount: " + _connectCount + " : Attempting Reconnect :: Exception Message: " + _ex.Message);
                        if (_connectCount > 4)
                        {
                            Enginelog.Error(objSshInfo.SSHHost + " : ExecuteDBCommands Failed to Connect : Exception Message: " + _ex.Message);
                            throw;
                        }
                    }
                }

                if (client != null)
                {
                    Enginelog.Info(" PSSHConnMutex: ExecuteDBCommands calling ProcessSubAuthentication for Server: " + objSshInfo.SSHHost.ToString());

                    using (SubAuthentication subAuth = new SubAuthentication())
                    {
                        if (sshConnOpt == SSHConnFlag.RSession)
                        {
                            terminal = subAuth.ProcessSubAuthentication(objSshInfo, client, shellPrompt);
                        }
                        else if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            client = subAuth.ProcessSubAuthentication(objSshInfo, client, shellPrompt);
                        }
                    }

                    Enginelog.Info(" PSSHConnMutex: ExecuteDBCommands called ProcessSubAuthentication for Server: " + objSshInfo.SSHHost.ToString());
                    int _errorCount = 0;

                    for (int i = 0; i < strCommand.Length; i++)
                    {
                        if (i == strCommand.Length - 1)
                        {
                            Enginelog.Info(" PSSHConnMutex ExecuteDBCommands started on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i]);
                            if (sshConnOpt == SSHConnFlag.RSession)
                            {
                                terminal.Process();
                                terminal.Process();
                                Thread.Sleep(1000);

                                terminal.SendToServer(strCommand[i] + "\n");
                                terminal.Expect(">", expectTime);
                                Thread.Sleep(5000);
                                strOut = terminal.ReceivedData;
                            }
                            else if (sshConnOpt == SSHConnFlag.JSession)
                            {
                                strOut = client.SendWait(strCommand[i], @"SQL>", 1200000);
                                Thread.Sleep(1000);
                            }

                            if (strOut.Contains(strCommand[i]))
                            {
                                strOut = strOut.Replace(strCommand[i] + "\r\n", "");
                            }

                            _strOuts[i] = strOut;
                            Enginelog.Info("PSSHConnMutex ExecuteDBCommands Completed on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i] + " Output: " + strOut);

                        }
                        else
                        {
                            Enginelog.Info(" PSSHConnMutex ExecuteDBCommands1 started on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i]);
                            if (sshConnOpt == SSHConnFlag.RSession)
                            {
                                terminal.Process();
                                terminal.Process();
                                Thread.Sleep(1000);
                                terminal.SendToServer(strCommand[i] + "\n");
                                terminal.Expect(_reg, expectTime);
                                Thread.Sleep(1000);
                                strOut = terminal.ReceivedData;
                            }
                            else if (sshConnOpt == SSHConnFlag.JSession)
                            {
                                strOut = client.Send(strCommand[i], 600000);
                                Thread.Sleep(1000);
                            }

                            _strOuts[i] = strOut;
                            Enginelog.Info("PSSHConnMutex ExecuteDBCommands1 Completed on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i] + " Output: " + strOut);
                        }

                        if (strOut.Contains("sqlplus:  not found"))
                        {
                            if (_errorCount > 3)
                            {
                                Enginelog.Error("PSSHConnMutex ExecuteDBCommands :ERROR: sqlplus:  not found on " + objSshInfo.SSHHost);
                                break;
                            }
                            Enginelog.Error("PSSHConnMutex ExecuteDBCommands Error: SQLPLUS NOT FOUND on: " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " With Outputs: " + string.Join(",", _strOuts));
                            i = 0;
                            _errorCount++;
                        }
                    }
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex client null While ExecuteDBCommands On Server: " + objSshInfo.SSHHost);
                }
            }
            catch (Rebex.Net.SshException sshEx)
            {
                if (sshEx.InnerException != null)
                    Enginelog.Error("InnerException: PSSHConnMutex while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " Ssh Inner Exception Messege: " + sshEx.Message);

                Enginelog.Error("Exception: PSSHConnMutex while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " Ssh Exception Messege: " + sshEx.Message);
                throw sshEx;
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    Enginelog.Error("InnerException: PSSHConnMutex while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " exc Inner Exce Messege: " + exc.Message);

                Enginelog.Error("Exception: PSSHConnMutex while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " Exc Messege: " + exc.Message);
                throw exc;
            }
            finally
            {
                if (sshConnOpt == SSHConnFlag.RSession)
                {
                    Enginelog.Info("PSSHConnMutex ExecuteDBCommands1 started exit on Server: " + objSshInfo.SSHHost.ToString());
                    terminal.Process();
                    terminal.SendToServer("exit" + "\n");
                    terminal.Expect(_reg, 1000);
                    Thread.Sleep(500);
                    string strOut1 = terminal.ReceivedData;
                    Enginelog.Info("PSSHConnMutex ExecuteDBCommands1 exit competed on Server: " + objSshInfo.SSHHost.ToString() + " Command: exit Output: " + strOut1);
                    terminal.Unbind();


                }
                else
                {
                    Enginelog.Info("PSSHConnMutex ExecuteDBCommands started exit on Server: " + objSshInfo.SSHHost.ToString());
                    string strOut1 = client.Send("exit");
                    Enginelog.Info("PSSHConnMutex ExecuteDBCommands exit competed on Server: " + objSshInfo.SSHHost.ToString() + " Command: exit Output: " + strOut1);
                }

                if (DisconnectAndRemoveSSHSession(client))
                {
                    Enginelog.Info("PSSHConnMutex ExecuteDBCommands DisconnectAndRemoveSSHSession Completed on Server: " + objSshInfo.SSHHost.ToString());
                }
            }
            return strOut;
        }

        public string ExecuteDBCommandsWithPosition(SSHServerInfo objSshInfo, string[] strCommand, string shellPrpt, int dbprptPos)
        {
            string strOut = string.Empty;
            strOut = ExecuteDBCommandsWithPosition(objSshInfo, strCommand, expectTime, shellPrpt, dbprptPos);
            return strOut;
        }

        public string ExecuteDBCommandsWithPosition(SSHServerInfo objSshInfo, string[] strCommand, int waitTime, string shellPrpt, int dbprptPos)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrpt);
            dynamic client = null;
            VirtualTerminal terminal = null;
            string[] _strOuts = new string[strCommand.Length];

            shellPrompt = shellPrpt;
            expectTime = waitTime;

            try
            {
                int _connectCount = 0;
                while (true)
                {
                    try
                    {
                        client = CreateSSHSession(objSshInfo);

                        if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            if (client.Ssh.Connected)
                            {
                                Enginelog.Info(" PSSHConnMutex: ExecuteDBCommands Server: " + objSshInfo.SSHHost.ToString() + " JSession Connected ...!");
                                break;
                            }
                        }
                        else
                        {
                            if (!client.IsDisposed)
                            {
                                Enginelog.Info(" PSSHConnMutex: ExecuteDBCommands Server: " + objSshInfo.SSHHost.ToString() + " RSession Connected ...!");
                                break;
                            }
                        }

                        Thread.Sleep(5000);

                    }
                    catch (Exception _ex)
                    {
                        _connectCount++;
                        Thread.Sleep(5000);

                        Enginelog.Error(objSshInfo.SSHHost + " : ExecuteDBCommands Connection Failed _connectCount: " + _connectCount + " : Attempting Reconnect :: Exception Message: " + _ex.Message);
                        if (_connectCount > 4)
                        {
                            Enginelog.Error(objSshInfo.SSHHost + " : ExecuteDBCommands Failed to Connect : Exception Message: " + _ex.Message);
                            throw;
                        }
                    }
                }

                if (client != null)
                {
                    Enginelog.Info(" PSSHConnMutex: ExecuteDBCommands calling ProcessSubAuthentication for Server: " + objSshInfo.SSHHost.ToString());

                    using (SubAuthentication subAuth = new SubAuthentication())
                    {
                        if (sshConnOpt == SSHConnFlag.RSession)
                        {
                            terminal = subAuth.ProcessSubAuthentication(objSshInfo, client, shellPrompt);
                        }
                        else if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            client = subAuth.ProcessSubAuthentication(objSshInfo, client, shellPrompt);
                        }
                    }

                    Enginelog.Info(" PSSHConnMutex: ExecuteDBCommands called ProcessSubAuthentication for Server: " + objSshInfo.SSHHost.ToString());
                    int _errorCount = 0;

                    for (int i = 0; i < strCommand.Length; i++)
                    {
                        if ((i + 1) >= dbprptPos)
                        {
                            Enginelog.Info(" PSSHConnMutex ExecuteDBCommands started on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i]);
                            if (sshConnOpt == SSHConnFlag.RSession)
                            {
                                terminal.Process();
                                terminal.Process();
                                Thread.Sleep(1000);

                                terminal.SendToServer(strCommand[i] + "\n");
                                terminal.Expect(">", expectTime);
                                Thread.Sleep(5000);
                                strOut = terminal.ReceivedData;
                            }
                            else if (sshConnOpt == SSHConnFlag.JSession)
                            {
                                strOut = client.SendWait(strCommand[i], @"SQL>", 1200000);
                                Thread.Sleep(1000);
                            }

                            if (strOut.Contains(strCommand[i]))
                            {
                                strOut = strOut.Replace(strCommand[i] + "\r\n", "");
                            }

                            _strOuts[i] = strOut;
                            Enginelog.Info("PSSHConnMutex ExecuteDBCommands Completed on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i] + " Output: " + strOut);

                        }
                        else
                        {
                            Enginelog.Info(" PSSHConnMutex ExecuteDBCommands1 started on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i]);
                            if (sshConnOpt == SSHConnFlag.RSession)
                            {
                                terminal.Process();
                                terminal.Process();
                                Thread.Sleep(1000);
                                terminal.SendToServer(strCommand[i] + "\n");
                                terminal.Expect(_reg, expectTime);
                                Thread.Sleep(1000);
                                strOut = terminal.ReceivedData;
                            }
                            else if (sshConnOpt == SSHConnFlag.JSession)
                            {
                                strOut = client.Send(strCommand[i], 600000);
                                Thread.Sleep(1000);
                            }

                            _strOuts[i] = strOut;
                            Enginelog.Info("PSSHConnMutex ExecuteDBCommands1 Completed on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i] + " Output: " + strOut);
                        }

                        if (strOut.Contains("sqlplus:  not found"))
                        {
                            if (_errorCount > 3)
                            {
                                Enginelog.Error("PSSHConnMutex ExecuteDBCommands :ERROR: sqlplus:  not found on " + objSshInfo.SSHHost);
                                break;
                            }
                            Enginelog.Error("PSSHConnMutex ExecuteDBCommands Error: SQLPLUS NOT FOUND on: " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " With Outputs: " + string.Join(",", _strOuts));
                            i = 0;
                            _errorCount++;
                        }
                    }
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex client null While ExecuteDBCommands On Server: " + objSshInfo.SSHHost);
                }
            }
            catch (Rebex.Net.SshException sshEx)
            {
                if (sshEx.InnerException != null)
                    Enginelog.Error("InnerException: PSSHConnMutex while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " Ssh Inner Exception Messege: " + sshEx.Message);

                Enginelog.Error("Exception: PSSHConnMutex while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " Ssh Exception Messege: " + sshEx.Message);
                throw sshEx;
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    Enginelog.Error("InnerException: PSSHConnMutex while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " exc Inner Exce Messege: " + exc.Message);

                Enginelog.Error("Exception: PSSHConnMutex while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " Exc Messege: " + exc.Message);
                throw exc;
            }
            finally
            {
                if (sshConnOpt == SSHConnFlag.RSession)
                {
                    Enginelog.Info("PSSHConnMutex ExecuteDBCommands1 started exit on Server: " + objSshInfo.SSHHost.ToString());
                    terminal.Process();
                    terminal.SendToServer("exit" + "\n");
                    terminal.Expect(_reg, expectTime);
                    Thread.Sleep(1000);
                    string strOut1 = terminal.ReceivedData;
                    Enginelog.Info("PSSHConnMutex ExecuteDBCommands1 exit competed on Server: " + objSshInfo.SSHHost.ToString() + " Command: exit Output: " + strOut1);
                    // terminal.Unbind();
                }
                else
                {
                    Enginelog.Info("PSSHConnMutex ExecuteDBCommands started exit on Server: " + objSshInfo.SSHHost.ToString());
                    string strOut1 = client.Send("exit");
                    Enginelog.Info("PSSHConnMutex ExecuteDBCommands exit competed on Server: " + objSshInfo.SSHHost.ToString() + " Command: exit Output: " + strOut1);
                }

                if (DisconnectAndRemoveSSHSession(client))
                {
                    Enginelog.Info("PSSHConnMutex ExecuteDBCommands DisconnectAndRemoveSSHSession Completed on Server: " + objSshInfo.SSHHost.ToString());
                }
            }
            return strOut;
        }

        public string ExecuteDBCommandsWithSession(dynamic client, SSHServerInfo objSshInfo, string[] strCommand, string shellPrpt)
        {
            string strOut = string.Empty;
            strOut = ExecuteDBCommandsWithSession(client, objSshInfo, strCommand, expectTime, shellPrpt);
            return strOut;
        }

        public string ExecuteDBCommandsWithSession(dynamic client, SSHServerInfo objSshInfo, string[] strCommand, int waitTime, string shellPrpt)
        {
            //dynamic client = null;
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrpt);

            VirtualTerminal terminal = null;
            string[] _strOuts = new string[strCommand.Length];

            shellPrompt = shellPrpt;
            expectTime = waitTime;

            try
            {
#pragma warning disable CS0219 // The variable '_connectCount' is assigned but its value is never used
                int _connectCount = 0;
#pragma warning restore CS0219 // The variable '_connectCount' is assigned but its value is never used

                if (client != null)
                {
                    Enginelog.Info("PSSHConnMutex ExecuteDBCommandsWithSession calling ProcessSubAuthentication");

                    using (SubAuthentication subAuth = new SubAuthentication())
                    {
                        if (sshConnOpt == SSHConnFlag.RSession)
                        {
                            terminal = subAuth.ProcessSubAuthentication(objSshInfo, client, shellPrompt);
                        }
                        else if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            client = subAuth.ProcessSubAuthentication(objSshInfo, client, shellPrompt);
                        }
                    }

                    Enginelog.Info("PSSHConnMutex ExecuteDBCommandsWithSession called ProcessSubAuthentication for Server: " + objSshInfo.SSHHost.ToString());
                    int _errorCount = 0;

                    for (int i = 0; i < strCommand.Length; i++)
                    {
                        if (i == strCommand.Length - 1)
                        {
                            Enginelog.Info("PSSHConnMutex ExecuteDBCommandsWithSession started on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i]);
                            if (sshConnOpt == SSHConnFlag.RSession)
                            {
                                terminal.Process();
                                terminal.Process();
                                Thread.Sleep(1000);

                                terminal.SendToServer(strCommand[i] + "\n");
                                terminal.Expect(_reg, expectTime);
                                Thread.Sleep(5000);
                                strOut = terminal.ReceivedData;
                            }
                            else if (sshConnOpt == SSHConnFlag.JSession)
                            {
                                strOut = client.SendWait(strCommand[i], @"SQL>", 1200000);
                                Thread.Sleep(1000);
                            }

                            if (strOut.Contains(strCommand[i]))
                            {
                                strOut = strOut.Replace(strCommand[i] + "\r\n", "");
                            }

                            _strOuts[i] = strOut;
                            Enginelog.Info("PSSHConnMutex ExecuteDBCommandsWithSession Completed on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i] + " Output: " + strOut);

                        }
                        else
                        {
                            Enginelog.Info("PSSHConnMutex ExecuteDBCommandsWithSession started on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i]);
                            if (sshConnOpt == SSHConnFlag.RSession)
                            {
                                terminal.Process();
                                terminal.Process();
                                Thread.Sleep(1000);
                                terminal.SendToServer(strCommand[i] + "\n");
                                terminal.Expect(_reg, expectTime);
                                Thread.Sleep(1000);
                                strOut = terminal.ReceivedData;
                            }
                            else if (sshConnOpt == SSHConnFlag.JSession)
                            {
                                strOut = client.Send(strCommand[i], 600000);
                                Thread.Sleep(1000);
                            }

                            _strOuts[i] = strOut;
                            Enginelog.Info("PSSHConnMutex ExecuteDBCommandsWithSession Completed on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i] + " Output: " + strOut);
                        }

                        if (strOut.Contains("sqlplus:  not found"))
                        {
                            if (_errorCount > 3)
                            {
                                Enginelog.Error("PSSHConnMutex ExecuteDBCommandsWithSession :ERROR: sqlplus:  not found on " + objSshInfo.SSHHost);
                                break;
                            }
                            Enginelog.Error("PSSHConnMutex ExecuteDBCommandsWithSession Error: SQLPLUS NOT FOUND on: " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " With Outputs: " + string.Join(",", _strOuts));
                            i = 0;
                            _errorCount++;
                        }
                    }
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex client null While ExecuteDBCommands On Server: " + objSshInfo.SSHHost);
                }
            }
            catch (Rebex.Net.SshException sshEx)
            {
                if (sshEx.InnerException != null)
                    Enginelog.Error("InnerException: PSSHConnMutex while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " Ssh Inner Exception Messege: " + sshEx.Message);

                Enginelog.Error("Exception: PSSHConnMutex while ExecuteDBCommands  on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " Ssh Exception Messege: " + sshEx.Message);
                throw sshEx;
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    Enginelog.Error("InnerException: PSSH(ExecuteDBCommandsWithSession): while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " exc Inner Exce Messege: " + exc.Message);

                Enginelog.Error("Exception: PSSH(ExecuteDBCommandsWithSession): while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " Exc Messege: " + exc.Message);
                throw exc;
            }
            finally
            {
                //if (sshConnOpt == SSHConnFlag.RSession)
                //{
                //    terminal.Unbind();
                //} 
            }
            return strOut;
        }

        public string ExecuteDBCommandsWithSession(ref dynamic client, string[] strCommand, string shellPrpt)
        {
            string strOut = string.Empty;
            strOut = ExecuteDBCommandsWithSession(ref client, strCommand, expectTime, shellPrpt);
            return strOut;
        }

        public string ExecuteDBCommandsWithSession(ref dynamic client, string[] strCommand, int waitTime, string shellPrpt)
        {
            //dynamic client = null;
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrpt);

            VirtualTerminal terminal = null;
            string[] _strOuts = new string[strCommand.Length];

            shellPrompt = shellPrpt;
            expectTime = waitTime;

            try
            {
#pragma warning disable CS0219 // The variable '_connectCount' is assigned but its value is never used
                int _connectCount = 0;
#pragma warning restore CS0219 // The variable '_connectCount' is assigned but its value is never used

                if (client != null)
                {
                    Enginelog.Info("PSSHConnMutex ExecuteDBCommandsWithRefSession calling ProcessSubAuthentication");
                    if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        //terminal = new VirtualTerminal(80, 400);
                        //terminal.Bind(client);
                        terminal = client;

                    }
                    else if (sshConnOpt == SSHConnFlag.JSession)
                    {

                    }

                    int _errorCount = 0;

                    for (int i = 0; i < strCommand.Length; i++)
                    {
                        if (i == strCommand.Length - 1)
                        {
                            Enginelog.Info("PSSHConnMutex ExecuteDBCommandsWithRefSession started for Command: " + strCommand[i]);
                            if (sshConnOpt == SSHConnFlag.RSession)
                            {
                                terminal.Process();
                                terminal.Process();
                                Thread.Sleep(1000);

                                terminal.SendToServer(strCommand[i] + "\n");
                                terminal.Expect(">", expectTime);
                                Thread.Sleep(5000);
                                strOut = terminal.ReceivedData;
                            }
                            else if (sshConnOpt == SSHConnFlag.JSession)
                            {
                                strOut = client.SendWait(strCommand[i], @"SQL>", 1200000);
                                Thread.Sleep(1000);
                            }

                            if (strOut.Contains(strCommand[i]))
                            {
                                strOut = strOut.Replace(strCommand[i] + "\r\n", "");
                            }

                            _strOuts[i] = strOut;
                            Enginelog.Info("PSSHConnMutex ExecuteDBCommandsWithRefSession Completed for Command: " + strCommand[i] + " Output: " + strOut);

                        }
                        else
                        {
                            Enginelog.Info("PSSHConnMutex ExecuteDBCommandsWithRefSession started for Command: " + strCommand[i]);
                            if (sshConnOpt == SSHConnFlag.RSession)
                            {
                                terminal.Process();
                                terminal.Process();
                                Thread.Sleep(1000);
                                terminal.SendToServer(strCommand[i] + "\n");
                                terminal.Expect(_reg, expectTime);
                                Thread.Sleep(1000);
                                strOut = terminal.ReceivedData;
                            }
                            else if (sshConnOpt == SSHConnFlag.JSession)
                            {
                                strOut = client.Send(strCommand[i], 600000);
                                Thread.Sleep(1000);
                            }

                            _strOuts[i] = strOut;
                            Enginelog.Info("PSSHConnMutex ExecuteDBCommandsWithRefSession Completed for Command: " + strCommand[i] + " Output: " + strOut);
                        }

                        if (strOut.Contains("sqlplus:  not found"))
                        {
                            if (_errorCount > 3)
                            {
                                Enginelog.Error("PSSHConnMutex ExecuteDBCommandsWithRefSession :ERROR: sqlplus:  not found ");
                                break;
                            }
                            Enginelog.Error("PSSHConnMutex ExecuteDBCommandsWithRefSession Error: SQLPLUS NOT FOUND: Executed All Commands: " + string.Join(",", strCommand) + " With Outputs: " + string.Join(",", _strOuts));
                            i = 0;
                            _errorCount++;
                        }
                    }
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex client null While ExecuteDBCommandsWithRefSession");
                }
            }
            catch (Rebex.Net.SshException sshEx)
            {
                if (sshEx.InnerException != null)
                    Enginelog.Error("InnerException: PSSHConnMutex while ExecuteDBCommandsWithRefSession: Executed All Commands: " + string.Join(",", strCommand) + " Ssh Inner Exception Messege: " + sshEx.Message);

                Enginelog.Error("Exception: PSSHConnMutex while ExecuteDBCommandsWithRefSession: Executed All Commands: " + string.Join(",", strCommand) + " Ssh Exception Messege: " + sshEx.Message);
                throw sshEx;
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    Enginelog.Error("InnerException: PSSH(ExecuteDBCommandsWithRefSession): Executed All Commands: " + string.Join(",", strCommand) + " exc Inner Exce Messege: " + exc.Message);

                Enginelog.Error("Exception: PSSH(ExecuteDBCommandsWithRefSession): Executed All Commands: " + string.Join(",", strCommand) + " Exc Messege: " + exc.Message);
                throw exc;
            }
            //finally
            //{
            //    if (sshConnOpt == SSHConnFlag.RSession)
            //    {
            //        client = terminal;
            //    } 
            //}
            return strOut;
        }

        public string ExecuteDBCommandWithSession(dynamic client, string command, string shellPrpt)
        {
            string strOut = string.Empty;
            strOut = ExecuteDBCommandWithSession(client, command, expectTime, shellPrpt);
            return strOut;
        }

        public string ExecuteDBCommandWithSession(dynamic client, string command, int waitTime, string shellPrpt)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrpt);

            VirtualTerminal terminal = null;

            shellPrompt = shellPrpt;
            expectTime = waitTime;

            try
            {
                if (client != null)
                {
                    Enginelog.Info("PSSHConnMutex ExecuteDBCommandWithSession started for Command: " + command);
                    if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        terminal = client;
                        //terminal.Bind(client);

                        terminal.Process();
                        terminal.Process();
                        Thread.Sleep(1000);

                        terminal.SendToServer(command + "\n");
                        terminal.Expect(">", expectTime);
                        //terminal.Expect(_reg, expectTime);
                        Thread.Sleep(5000);
                        strOut = terminal.ReceivedData;
                    }
                    else if (sshConnOpt == SSHConnFlag.JSession)
                    {
                        strOut = client.SendWait(command, @"SQL>", 1200000);  //changed prompt for postgres
                                                                              //strOut = client.SendWait(command, shellPrpt, 1200000);  
                        Thread.Sleep(1000);
                    }
                    Enginelog.Info("PSSHConnMutex ExecuteDBCommandWithSession completed for Command: " + command);
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex ExecuteDBCommandWithSession: client null While ExecuteDBCommands for command: " + command);
                }
            }
            catch (Rebex.Net.SshException sshEx)
            {
                if (sshEx.InnerException != null)
                    Enginelog.Error("InnerException: PSSH(ExecuteDBCommandWithSession): while ExecuteDBCommands for command: " + command + " : " + sshEx.Message);

                Enginelog.Error("Exception: PSSH(ExecuteDBCommandWithSession): while ExecuteDBCommands for command: " + command + " : " + sshEx.Message);
                throw sshEx;
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    Enginelog.Error("InnerException: PSSH(ExecuteDBCommandsWithSession): while ExecuteDBCommands for command: " + command + " : InnerException: " + exc.Message);

                Enginelog.Error("Exception: PSSH(ExecuteDBCommandsWithSession): while ExecuteDBCommands for command: " + command + " : Exc Messege: " + exc.Message);
                throw exc;
            }
            finally
            {
                //if (sshConnOpt == SSHConnFlag.RSession)
                //{
                //    terminal.Unbind();
                //}
            }
            return strOut;
        }

        public string _ExecuteddbbCommandsWithSession(dynamic client, string[] strCommand, string shellPrpt)
        {
            //dynamic client = null;
            string strOut = string.Empty;
            shellPrpt = "\\$";
            Regex _reg = new Regex(shellPrpt);

            VirtualTerminal terminal = null;
            string[] _strOuts = new string[strCommand.Length];

            shellPrompt = shellPrpt;
            //   expectTime = waitTime;

            try
            {
#pragma warning disable CS0219 // The variable '_connectCount' is assigned but its value is never used
                int _connectCount = 0;
#pragma warning restore CS0219 // The variable '_connectCount' is assigned but its value is never used

                if (client != null)
                {
                    Enginelog.Info("PSSHConnMutex ExecuteDBCommandsWithRefSession calling ProcessSubAuthentication");
                    if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        //terminal = new VirtualTerminal(80, 400);
                        //terminal.Bind(client);
                        terminal = client;

                    }
                    else if (sshConnOpt == SSHConnFlag.JSession)
                    {

                    }

                    int _errorCount = 0;

                    for (int i = 0; i < strCommand.Length; i++)
                    {
                        if (i == strCommand.Length - 1)
                        {
                            Enginelog.Info("PSSHConnMutex ExecuteDBCommandsWithRefSession started for Command: " + strCommand[i]);
                            if (sshConnOpt == SSHConnFlag.RSession)
                            {
                                terminal.Process();
                                terminal.Process();
                                Thread.Sleep(500);

                                terminal.SendToServer(strCommand[i] + "\n");
                                terminal.Expect(">", expectTime);
                                Thread.Sleep(2000);
                                strOut = terminal.ReceivedData;
                            }
                            else if (sshConnOpt == SSHConnFlag.JSession)
                            {
                                strOut = client.SendWait(strCommand[i], @"SQL>", 1200000);
                                Thread.Sleep(1000);
                            }

                            if (strOut.Contains(strCommand[i]))
                            {
                                strOut = strOut.Replace(strCommand[i] + "\r\n", "");
                            }

                            _strOuts[i] = strOut;
                            Enginelog.Info("PSSHConnMutex ExecuteDBCommandsWithRefSession Completed for Command: " + strCommand[i] + " Output: " + strOut);

                        }
                        else
                        {
                            Enginelog.Info("PSSHConnMutex ExecuteDBCommandsWithRefSession started for Command: " + strCommand[i]);
                            if (sshConnOpt == SSHConnFlag.RSession)
                            {
                                terminal.Process();
                                terminal.Process();
                                Thread.Sleep(500);
                                terminal.SendToServer(strCommand[i] + "\n");
                                terminal.Expect(_reg, 15000);
                                Thread.Sleep(1000);
                                strOut = terminal.ReceivedData;
                            }
                            else if (sshConnOpt == SSHConnFlag.JSession)
                            {
                                strOut = client.Send(strCommand[i], 600000);
                                Thread.Sleep(1000);
                            }

                            _strOuts[i] = strOut;
                            Enginelog.Info("PSSHConnMutex ExecuteDBCommandsWithRefSession Completed for Command: " + strCommand[i] + " Output: " + strOut);
                        }

                        if (strOut.Contains("sqlplus:  not found"))
                        {
                            if (_errorCount > 3)
                            {
                                Enginelog.Error("PSSHConnMutex ExecuteDBCommandsWithRefSession :ERROR: sqlplus:  not found ");
                                break;
                            }
                            Enginelog.Error("PSSHConnMutex ExecuteDBCommandsWithRefSession Error: SQLPLUS NOT FOUND: Executed All Commands: " + string.Join(",", strCommand) + " With Outputs: " + string.Join(",", _strOuts));
                            i = 0;
                            _errorCount++;
                        }
                    }
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex client null While ExecuteDBCommandsWithRefSession");
                }
            }
            catch (Rebex.Net.SshException sshEx)
            {
                if (sshEx.InnerException != null)
                    Enginelog.Error("InnerException: PSSHConnMutex while ExecuteDBCommandsWithRefSession: Executed All Commands: " + string.Join(",", strCommand) + " Ssh Inner Exception Messege: " + sshEx.Message);

                Enginelog.Error("Exception: PSSHConnMutex while ExecuteDBCommandsWithRefSession: Executed All Commands: " + string.Join(",", strCommand) + " Ssh Exception Messege: " + sshEx.Message);
                throw sshEx;
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    Enginelog.Error("InnerException: PSSH(ExecuteDBCommandsWithRefSession): Executed All Commands: " + string.Join(",", strCommand) + " exc Inner Exce Messege: " + exc.Message);

                Enginelog.Error("Exception: PSSH(ExecuteDBCommandsWithRefSession): Executed All Commands: " + string.Join(",", strCommand) + " Exc Messege: " + exc.Message);
                throw exc;
            }
            //finally
            //{
            //    if (sshConnOpt == SSHConnFlag.RSession)
            //    {
            //        client = terminal;
            //    } 
            //}
            return strOut;
        }

        public string ExecuteDBCommandWithSession(dynamic client, string command, int waitTime, string shellPrpt, string[] preExeCmds)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrpt);

            VirtualTerminal terminal = null;

            shellPrompt = shellPrpt;
            expectTime = waitTime;

            try
            {
                if (client != null)
                {
                    Enginelog.Info("PSSHConnMutex ExecuteDBCommandWithSession started for Command: " + command);
                    if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        if (preExeCmds != null && preExeCmds.Count() > 0)
                        {
                            ExecuteDBCommandsWithSession(ref client, preExeCmds, shellPrompt);
                        }

                        terminal = client;
                        //terminal.Bind(client);

                        terminal.Process();
                        terminal.Process();
                        Thread.Sleep(1000);

                        terminal.SendToServer(command + "\n");
                        terminal.Expect(">", expectTime);
                        Thread.Sleep(5000);
                        strOut = terminal.ReceivedData;
                    }
                    else if (sshConnOpt == SSHConnFlag.JSession)
                    {
                        strOut = client.SendWait(command, @"SQL>", 1200000);
                        Thread.Sleep(1000);
                    }
                    Enginelog.Info("PSSHConnMutex ExecuteDBCommandWithSession completed for Command: " + command);
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex ExecuteDBCommandWithSession: client null While ExecuteDBCommands for command: " + command);
                }
            }
            catch (Rebex.Net.SshException sshEx)
            {
                if (sshEx.InnerException != null)
                    Enginelog.Error("InnerException: PSSH(ExecuteDBCommandWithSession): while ExecuteDBCommands for command: " + command + " : " + sshEx.Message);

                Enginelog.Error("Exception: PSSH(ExecuteDBCommandWithSession): while ExecuteDBCommands for command: " + command + " : " + sshEx.Message);
                throw sshEx;
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    Enginelog.Error("InnerException: PSSH(ExecuteDBCommandsWithSession): while ExecuteDBCommands for command: " + command + " : InnerException: " + exc.Message);

                Enginelog.Error("Exception: PSSH(ExecuteDBCommandsWithSession): while ExecuteDBCommands for command: " + command + " : Exc Messege: " + exc.Message);
                throw exc;
            }
            finally
            {
                //if (sshConnOpt == SSHConnFlag.RSession)
                //{
                //    terminal.Unbind();
                //}
            }
            return strOut;
        }

        public string ExecuteCommandsWithPassword(SSHServerInfo objSshInfo, string[] strCommand, string shellPrpt, string password, int passwordPos, string passwordPrpt)
        {
            string strOut = string.Empty;
            strOut = ExecuteCommandsWithPassword(objSshInfo, strCommand, expectTime, shellPrpt, password, passwordPos, passwordPrpt);
            return strOut;
        }

        public string ExecuteCommandsWithPassword(SSHServerInfo objSshInfo, string[] strCommand, int waitTime, string shellPrpt, string password, int passwordPos, string passwordPrpt)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrpt, RegexOptions.RightToLeft);
            dynamic client = null;
            VirtualTerminal terminal = null;

            try
            {
                int _connectCount = 0;
                while (true)
                {
                    try
                    {
                        client = CreateSSHSession(objSshInfo);

                        if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            if (client.Ssh.Connected)
                            {
                                Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword Server: " + objSshInfo.SSHHost.ToString() + " JSession Connected ...!");
                                break;
                            }
                        }
                        else
                        {
                            // if (client.IsConnected)
                            if (!client.IsDisposed)
                            {
                                Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword Server: " + objSshInfo.SSHHost.ToString() + " RSession Connected ...!");
                                break;
                            }
                        }

                        Thread.Sleep(1000);

                    }
                    catch (Exception _ex)
                    {
                        _connectCount++;
                        Thread.Sleep(2000);

                        Enginelog.Error(objSshInfo.SSHHost + " : ExecuteCommandsWithPassword Connection Failed _connectCount: " + _connectCount + " : Attempting Reconnect :: Exception Message: " + _ex.Message);
                        if (_connectCount > 4)
                        {
                            Enginelog.Error(objSshInfo.SSHHost + " : ExecuteCommandsWithPassword Failed to Connect: Exception Message: " + _ex.Message);
                            throw;
                        }
                    }
                }

                if (client != null)
                {
                    Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword calling ProcessSubAuthentication for Server: " + objSshInfo.SSHHost.ToString());

                    using (SubAuthentication subAuth = new SubAuthentication())
                    {
                        if (sshConnOpt == SSHConnFlag.RSession)
                        {
                            terminal = subAuth.ProcessSubAuthentication(objSshInfo, client, shellPrompt);
                        }
                        else if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            client = subAuth.ProcessSubAuthentication(objSshInfo, client, shellPrompt);
                        }
                    }

                    Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword called ProcessSubAuthentication for Server: " + objSshInfo.SSHHost.ToString());

                    for (int i = 0; i < strCommand.Length; i++)
                    {
                        Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword started on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i]);
                        if (sshConnOpt == SSHConnFlag.RSession)
                        {
                            Thread.Sleep(1000);
                            terminal.Process();
                            //terminal.Process();
                            Thread.Sleep(1000);

                            terminal.SendToServer(strCommand[i] + "\r\n");

                            if ((i + 1) == passwordPos)
                            {
                                terminal.Expect(passwordPrpt, waitTime);
                                strOut = terminal.ReceivedData;

                                if (!string.IsNullOrEmpty(password))
                                {
                                    terminal.Process();
                                    terminal.SendToServer(password + "\r\n");
                                    terminal.Expect(_reg, waitTime);
                                    strOut = terminal.ReceivedData;
                                }
                            }
                            else
                            {
                                Thread.Sleep(1000);
                                terminal.Expect(_reg, expectTime);
                                Thread.Sleep(2000);
                                strOut = terminal.ReceivedData;
                            }
                        }
                        else if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            if ((i + 1) == passwordPos)
                            {
                                strOut = client.SendWait(strCommand[i], passwordPrpt, false, waitTime);
                                if (!string.IsNullOrEmpty(password))
                                {
                                    strOut = client.SendWait(strCommand[i], _reg, waitTime);
                                }
                            }
                            else
                            {
                                strOut = client.SendWait(strCommand[i], _reg, waitTime);
                            }
                            Thread.Sleep(1000);
                        }

                        if (strOut.Contains(strCommand[i]))
                        {
                            strOut = strOut.Replace(strCommand[i] + "\r\n", "");
                        }

                        // Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword Completed on Server: " + objSshInfo.SSHHost.ToString() + " Output: " + strOut);
                        Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword Completed on Server: " + objSshInfo.SSHHost.ToString());

                    }
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex client null in ExecuteCommandsWithPassword On Server: " + objSshInfo.SSHHost);
                }
            }
            catch (Rebex.Net.SshException sshEx)
            {
                if (sshEx.InnerException != null)
                    Enginelog.Error("InnerException: PSSHConnMutex in ExecuteCommandsWithPassword on Host " + objSshInfo.SSHHost + " Ssh Inner Exception Messege: " + sshEx.Message);

                Enginelog.Error("Exception: PSSHConnMutex in ExecuteCommandsWithPassword on Host " + objSshInfo.SSHHost + " Ssh Exception Messege: " + sshEx.Message);
                throw sshEx;
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    Enginelog.Error("InnerException: PSSHConnMutex in ExecuteCommandsWithPassword on Host " + objSshInfo.SSHHost + " exc Inner Exce Messege: " + exc.Message);

                Enginelog.Error("Exception: PSSHConnMutex in ExecuteCommandsWithPassword on Host " + objSshInfo.SSHHost + " Exc Messege: " + exc.Message);
                throw exc;
            }
            finally
            {
                if (sshConnOpt == SSHConnFlag.RSession)
                {
                    Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword started exit on Server: " + objSshInfo.SSHHost.ToString());
                    terminal.Process();
                    terminal.SendToServer("exit" + "\n");
                    terminal.Expect(_reg, expectTime);
                    Thread.Sleep(1000);
                    string strOut1 = terminal.ReceivedData;
                    Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword exit competed on Server: " + objSshInfo.SSHHost.ToString() + " Command: exit Output: " + strOut);
                    terminal.Unbind();
                }
                else
                {
                    Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword started exit on Server: " + objSshInfo.SSHHost.ToString());
                    string strOut1 = client.Send("exit");
                    Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword exit competed on Server: " + objSshInfo.SSHHost.ToString() + " Command: exit Output: " + strOut);
                }

                if (DisconnectAndRemoveSSHSession(client))
                {
                    Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword DisconnectAndRemoveSSHSession Completed on Server: " + objSshInfo.SSHHost.ToString());
                }
            }

            shellPrompt = shellPrpt;
            expectTime = waitTime;

            return strOut;
        }

        public string ExecuteCommandsWithPasswordrsync(SSHServerInfo objSshInfo, string[] strCommand, string shellPrpt, string password, int passwordPos, string passwordPrpt)
        {
            string strOut = string.Empty;
            strOut = ExecuteCommandsWithPasswordrsync(objSshInfo, strCommand, expectTime, shellPrpt, password, passwordPos, passwordPrpt);
            return strOut;
        }

        public string ExecuteCommandsWithPasswordrsync(SSHServerInfo objSshInfo, string[] strCommand, int waitTime, string shellPrpt, string password, int passwordPos, string passwordPrpt)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrpt, RegexOptions.RightToLeft);
            dynamic client = null;
            VirtualTerminal terminal = null;

            try
            {
                int _connectCount = 0;
                while (true)
                {
                    try
                    {
                        client = CreateSSHSession(objSshInfo);

                        if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            if (client.Ssh.Connected)
                            {
                                Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword Server: " + objSshInfo.SSHHost.ToString() + " JSession Connected ...!");
                                break;
                            }
                        }
                        else
                        {
                            // if (client.IsConnected)
                            if (!client.IsDisposed)
                            {
                                Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword Server: " + objSshInfo.SSHHost.ToString() + " RSession Connected ...!");
                                break;
                            }
                        }

                        Thread.Sleep(5000);

                    }
                    catch (Exception _ex)
                    {
                        _connectCount++;
                        Thread.Sleep(5000);

                        Enginelog.Error(objSshInfo.SSHHost + " : ExecuteCommandsWithPassword Connection Failed _connectCount: " + _connectCount + " : Attempting Reconnect :: Exception Message: " + _ex.Message);
                        if (_connectCount > 4)
                        {
                            Enginelog.Error(objSshInfo.SSHHost + " : ExecuteCommandsWithPassword Failed to Connect: Exception Message: " + _ex.Message);
                            throw;
                        }
                    }
                }

                if (client != null)
                {
                    Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword calling ProcessSubAuthentication for Server: " + objSshInfo.SSHHost.ToString());

                    using (SubAuthentication subAuth = new SubAuthentication())
                    {
                        if (sshConnOpt == SSHConnFlag.RSession)
                        {
                            terminal = subAuth.ProcessSubAuthentication(objSshInfo, client, shellPrompt);
                        }
                        else if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            client = subAuth.ProcessSubAuthentication(objSshInfo, client, shellPrompt);
                        }
                    }

                    Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword called ProcessSubAuthentication for Server: " + objSshInfo.SSHHost.ToString());

                    for (int i = 0; i < strCommand.Length; i++)
                    {

                        Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword started on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i]);
                        if (sshConnOpt == SSHConnFlag.RSession)
                        {
                            Thread.Sleep(1000);
                            terminal.Process();
                            //terminal.Process();
                            Thread.Sleep(1000);

                            terminal.SendToServer(strCommand[i] + "\r\n");

                            if ((i + 1) == passwordPos)
                            {
                                terminal.Expect(passwordPrpt, waitTime);
                                strOut = terminal.ReceivedData;

                                if (!string.IsNullOrEmpty(password))
                                {
                                    terminal.Process();
                                    terminal.SendToServer(password + "\r\n");
                                    terminal.Expect(_reg, waitTime);
                                    strOut = terminal.ReceivedData;
                                }
                            }
                            else
                            {
                                Thread.Sleep(1000);
                                //terminal.Expect(shellPrpt, expectTime);
                                terminal.Expect("&", 10000);
                                Thread.Sleep(5000);
                                strOut = terminal.ReceivedData;
                            }
                        }
                        else if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            if ((i + 1) == passwordPos)
                            {
                                strOut = client.SendWait(strCommand[i], passwordPrpt, false, waitTime);
                                if (!string.IsNullOrEmpty(password))
                                {
                                    strOut = client.SendWait(strCommand[i], _reg, waitTime);
                                }
                            }
                            else
                            {
                                strOut = client.SendWait(strCommand[i], _reg, waitTime);
                            }
                            Thread.Sleep(1000);
                        }

                        if (strOut.Contains(strCommand[i]))
                        {
                            strOut = strOut.Replace(strCommand[i] + "\r\n", "");
                        }

                        // Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword Completed on Server: " + objSshInfo.SSHHost.ToString() + " Output: " + strOut);
                        Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword Completed on Server: " + objSshInfo.SSHHost.ToString());
                    }
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex client null in ExecuteCommandsWithPassword On Server: " + objSshInfo.SSHHost);
                }
            }
            catch (Rebex.Net.SshException sshEx)
            {
                if (sshEx.InnerException != null)
                    Enginelog.Error("InnerException: PSSHConnMutex in ExecuteCommandsWithPassword on Host " + objSshInfo.SSHHost + " Ssh Inner Exception Messege: " + sshEx.Message);

                Enginelog.Error("Exception: PSSHConnMutex in ExecuteCommandsWithPassword on Host " + objSshInfo.SSHHost + " Ssh Exception Messege: " + sshEx.Message);
                throw sshEx;
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    Enginelog.Error("InnerException: PSSHConnMutex in ExecuteCommandsWithPassword on Host " + objSshInfo.SSHHost + " exc Inner Exce Messege: " + exc.Message);

                Enginelog.Error("Exception: PSSHConnMutex in ExecuteCommandsWithPassword on Host " + objSshInfo.SSHHost + " Exc Messege: " + exc.Message);
                throw exc;
            }
            finally
            {
                if (sshConnOpt == SSHConnFlag.RSession)
                {
                    Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword started exit on Server: " + objSshInfo.SSHHost.ToString());
                    terminal.Process();
                    terminal.SendToServer("exit" + "\n");
                    terminal.Expect(_reg, expectTime);
                    Thread.Sleep(1000);
                    string strOut1 = terminal.ReceivedData;
                    Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword exit competed on Server: " + objSshInfo.SSHHost.ToString() + " Command: exit Output: " + strOut1);
                    terminal.Unbind();
                }
                else
                {
                    Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword started exit on Server: " + objSshInfo.SSHHost.ToString());
                    string strOut1 = client.Send("exit");
                    Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword exit competed on Server: " + objSshInfo.SSHHost.ToString() + " Command: exit Output: " + strOut);
                }

                if (DisconnectAndRemoveSSHSession(client))
                {
                    Enginelog.Info("PSSHConnMutex ExecuteCommandsWithPassword DisconnectAndRemoveSSHSession Completed on Server: " + objSshInfo.SSHHost.ToString());
                }
            }

            shellPrompt = shellPrpt;
            expectTime = waitTime;

            return strOut;
        }

        public string ExecuteCommandsWithSessionAndPassword(dynamic client, string[] strCommand, string shellPrpt, string password, int passwordPos, string passwordPrpt)
        {
            string strOut = string.Empty;
            strOut = ExecuteCommandsWithSessionAndPassword(client, strCommand, expectTime, shellPrpt, password, passwordPos, passwordPrpt);
            return strOut;
        }

        public string ExecuteCommandsWithSessionAndPassword(dynamic client, string[] strCommand, int waitTime, string shellPrpt, string password, int passwordPos, string passwordPrpt)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrpt);
            VirtualTerminal terminal = null;
            try
            {
                if (client != null)
                {
                    for (int i = 0; i < strCommand.Length; i++)
                    {

                        Enginelog.Info("PSSHConnMutex ExecuteCommandsWithSessionAndPassword: Command: " + strCommand[i]);
                        if (sshConnOpt == SSHConnFlag.RSession)
                        {
                            terminal = client;

                            terminal.Process();
                            terminal.Process();
                            Thread.Sleep(1000);

                            terminal.SendToServer(strCommand[i] + "\n");

                            if ((i + 1) == passwordPos)
                            {
                                terminal.Expect(passwordPrpt, waitTime);
                                strOut = terminal.ReceivedData;

                                if (!string.IsNullOrEmpty(password))
                                {
                                    terminal.Process();
                                    terminal.SendToServer(password + "\r\n");
                                    terminal.Expect(_reg, waitTime);
                                    strOut = terminal.ReceivedData;
                                }
                            }
                            else
                            {
                                terminal.Expect(_reg, expectTime);
                                Thread.Sleep(5000);
                                strOut = terminal.ReceivedData;
                            }
                        }
                        else if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            if ((i + 1) == passwordPos)
                            {
                                strOut = client.SendWait(strCommand[i], passwordPrpt, false, waitTime);
                                if (!string.IsNullOrEmpty(password))
                                {
                                    strOut = client.SendWait(strCommand[i], _reg, waitTime);
                                }
                            }
                            else
                            {
                                strOut = client.SendWait(strCommand[i], _reg, waitTime);
                            }
                            Thread.Sleep(1000);
                        }

                        if (strOut.Contains(strCommand[i]))
                        {
                            strOut = strOut.Replace(strCommand[i] + "\r\n", "");
                        }

                        Enginelog.Info("PSSHConnMutex ExecuteCommandsWithSessionAndPassword: Output: " + strOut);

                    }
                }
            }
            catch (Rebex.Net.SshException sshEx)
            {
                if (sshEx.InnerException != null)
                    Enginelog.Error("InnerException: PSSHConnMutex in ExecuteCommandsWithSessionAndPassword Ssh InnerException: " + sshEx.InnerException.Message);

                Enginelog.Error("Exception: PSSHConnMutex in ExecuteCommandsWithSessionAndPassword - Ssh Exception: " + sshEx.Message);
                throw sshEx;
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    Enginelog.Error("InnerException: PSSHConnMutex in ExecuteCommandsWithSessionAndPassword - InnerException: " + exc.InnerException.Message);

                Enginelog.Error("Exception: PSSHConnMutex in ExecuteCommandsWithSessionAndPassword - Exception: " + exc.Message);
                throw exc;
            }
            finally
            {

            }

            shellPrompt = shellPrpt;
            expectTime = waitTime;

            return strOut;
        }

        public string ExecuteOSCmmandWithSessionWithMaxDB(dynamic client, string shellPrompt, string command)
        {
            string strOut = string.Empty;
            strOut = ExecuteOSCmmandWithSessionWithMaxDB(client, shellPrompt, command, expectTime);
            return strOut;
        }

        public string ExecuteOSCmmandWithSessionWithMaxDB(dynamic client, string shellPrompt, string command, int timeout)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrompt);
            VirtualTerminal terminal = null;
            try
            {
                if (client != null)
                {
                    if (sshConnOpt == SSHConnFlag.JSession)
                    {
                        client.SetShellPrompt(shellPrompt, true);
                        strOut = client.SendWait(command, shellPrompt, true, timeout);
                    }
                    else if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        //terminal = new VirtualTerminal(80, 400);
                        //terminal.Bind(client);

                        terminal = client;
                        Thread.Sleep(1000);
                        terminal.Process();
                        Thread.Sleep(1000);
                        terminal.SendToServer(command + "\n");
                        terminal.Expect(_reg, timeout);
                        Thread.Sleep(3000);
                        strOut = terminal.ReceivedData;

                        if (!string.IsNullOrEmpty(strOut) && strOut.Contains(command + "\r\n"))
                        {
                            strOut = strOut.Replace(command + "\r\n", "");
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCmmand(with dynamic conn object), InnerException: " + exc.InnerException.Message.ToString());
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCmmand(with dynamic conn object), Exception: " + exc.Message.ToString());
                }
            }

            //finally
            //{
            //    if (sshConnOpt == SSHConnFlag.RSession)
            //    {
            //        if (terminal != null)
            //        {
            //            terminal.Unbind();
            //        }
            //        if (!terminal.IsDisposed)
            //        {
            //            terminal.Dispose();
            //        }
            //    }
            //}

            return strOut;
        }

        public string ExecuteOSCmmandForMSSql(SSHServerInfo objSshInfo, string shellPrompt, string command)
        {
            string strOut = string.Empty;
            strOut = ExecuteOSCmmandForMSSql(objSshInfo, shellPrompt, command, expectTime);
            return strOut;
        }

        public string ExecuteOSCmmandForMSSql(SSHServerInfo objSshInfo, string shellPrompt, string command, int timeout)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrompt, RegexOptions.RightToLeft);
            dynamic client = null;
            VirtualTerminal terminal = null;
            try
            {
                client = CreateSSHSession(objSshInfo);

                if (client != null)
                {
                    if (sshConnOpt == SSHConnFlag.JSession)
                    {
                        client.SetShellPrompt(shellPrompt, true);
                        strOut = client.SendWait(command, shellPrompt, true, timeout);
                    }
                    else if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        //terminal = new VirtualTerminal(80, 400);
                        //terminal.Bind(client);

                        terminal = client;
                        Thread.Sleep(1000);
                        terminal.Process();
                        //Thread.Sleep(4000);
                        terminal.SendToServer(command + "\r\n");
                        terminal.Expect("1>", timeout);
                        Thread.Sleep(2000);
                        strOut = terminal.ReceivedData;
                    }
                    Enginelog.Info("PSSHConnMutex Command(in ExecuteOSCmmand): " + command + " executed successfully : output: " + strOut);
                }

            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCmmand(with server conn details), InnerException: " + exc.InnerException.Message.ToString());
                    throw exc;
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCmmand(with server conn details), Exception: " + exc.Message.ToString());
                    throw exc;
                }
            }
            finally
            {
                //if (sshConnOpt == SSHConnFlag.RSession)
                //{
                //    if (terminal != null)
                //    {
                //        terminal.Unbind();
                //    }
                //    if (!terminal.IsDisposed)
                //    {
                //        terminal.Dispose();
                //    }
                //}
                DisconnectAndRemoveSSHSession(client);
            }

            return strOut;
        }

        public string ExecuteOSCmmandWithSession_New(dynamic client, string shellPrompt, string command, string user)
        {
            string strOut = string.Empty;
            strOut = ExecuteOSCmmandWithSession_New(client, shellPrompt, command, expectTime, user);
            return strOut;
        }

        public string ExecuteOSCmmandWithSession_New(dynamic client, string shellPrompt, string command, int timeout, string user)
        {
            string strOut = string.Empty;
            //Regex _reg = new Regex(shellPrompt);

            // Enginelog.Info("PSSHConnMutex ExecuteOSCmmandWithSession_New  shellPrompt: " + shellPrompt);
            VirtualTerminal terminal = null;
            Regex _reg = new Regex(shellPrompt, RegexOptions.RightToLeft);
            try
            {
                //  Enginelog.Info("PSSHConnMutex ExecuteOSCmmandWithSession_New executing command: " + command);

                if (client != null)
                {
                    if (sshConnOpt == SSHConnFlag.JSession)
                    {
                        client.SetShellPrompt(shellPrompt, true);
                        strOut = client.SendWait(command, shellPrompt, true, timeout);
                    }
                    else if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        //terminal = new VirtualTerminal(80, 400);
                        //terminal.Bind(client);
                        //    Enginelog.Info("----------------PSSHConnMutex ExecuteOSCmmandWithSession_New SSHConnFlag.RSession start---------------");
                        terminal = client;
                        Thread.Sleep(1000);
                        terminal.Process();

                        Thread.Sleep(1000);
                        terminal.SendToServer(command + "\n");
                        if (user == "sol")
                            terminal.Expect("$", timeout);
                        else if (user == "root")
                            terminal.Expect("#", timeout);
                        else
                            terminal.Expect(_reg, timeout);
                        Thread.Sleep(2000);
                        strOut = terminal.ReceivedData;

                        //   Enginelog.Info("----------------PSSHConnMutex ExecuteOSCmmandWithSession_New SSHConnFlag.RSession end---------------" + strOut);
                    }
                }

                Enginelog.Info("PSSHConnMutex ExecuteOSCmmandWithSession_New executing command completed: " + command + " Output: " + strOut);
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCmmandWithSession_New(with dynamic conn object), InnerException: " + exc.InnerException.Message.ToString());
                }
                else
                {
                    Enginelog.Error("PSSHConnMutex While ExecuteOSCmmandWithSession_New(with dynamic conn object), Exception: " + exc.Message.ToString());
                }
            }
            finally
            {
                if (sshConnOpt == SSHConnFlag.RSession)
                {
                    if (terminal != null)
                    {
                        terminal.Unbind();
                    }
                    if (!terminal.IsDisposed)
                    {
                        terminal.Dispose();
                    }
                }
                DisconnectAndRemoveSSHSession(client);
            }

            return strOut;
        }

        #endregion Common Execution methods - PSSH

        #endregion Session - PSSH

        public string GetDynamicPromt(string strOut)
        {
            int count = 0;
            string _Promt = string.Empty;

            string[] resultarray = Regex.Split(strOut, "\r\n");

            count = resultarray.Count();

            _Promt = resultarray[count - 1];

            if (_Promt.Contains("SQL>"))
            {
                return "SQL>";
            }
            if (_Promt.Contains("$"))
            {
                return "$";
            }
            if (_Promt.Contains("/"))
            {
                return "/";
            }
            if (_Promt.Contains("#"))
            {
                return "#";
            }
            if (_Promt.Contains(">"))
            {
                return ">";
            }
            return _Promt;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        private void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {

                }
            }

            _isDisposed = true;
        }

        ~PSSHMutex()
        {
            Dispose(false);
        }

    }

    #endregion PSSHMutex
}
